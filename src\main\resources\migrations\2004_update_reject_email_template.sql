--liquibase formatted sql
--changeset lamlt:2004

UPDATE `configurations` SET `value` = 'Label of notification ${notificationId} (Product Name: ${productName}) is rejected by ${sourceFullName}. Reason: ${reason}. <br> Please click <a href="http://192.7.86.23/label/update/${labelId}">here</a> to view detail' WHERE `configurations`.`id` = 'reject-label.alert.email.template';
UPDATE `configurations` SET `value` = 'Notification ${notificationId} (Product Name: ${productName}) has rejected product name. Reason ${reason}. <br> Please click <a href="http://192.7.86.23/notification/index">here</a> to view detail' WHERE `configurations`.`id` = 'reject-product-name.alert.email.template';
UPDATE `configurations` SET `value` = 'Notification ${notificationId} (Product Name: ${productName}) is rejected by ${sourceFullName}. Reason: ${reason}. <br> Please click <a href="http://192.7.86.23/notification/update/${notificationId}">here</a> to view detail' WHERE `configurations`.`id` = 'reject.alert.email.template';

--rollback;