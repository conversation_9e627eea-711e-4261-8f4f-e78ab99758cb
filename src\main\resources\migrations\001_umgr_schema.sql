--liquibase formatted sql

--changeset vietnq:1

CREATE TABLE users (
  id INTEGER NOT NULL,
  full_name VA<PERSON><PERSON><PERSON>(255),
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255),
  group_id INTEGER,
  avatar_uri MEDIUMTEXT,
  title VARCHAR(255),
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE groups (
  id INTEGER NOT NULL,
  name VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
  code VARCHAR(255),
  ascendant_id INTEGER,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE permissions (
  id VARCHAR(255) NOT NULL,
  description VARCHAR(255),
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE groups_permissions (
  id INTEGER NOT NULL,
  group_id INTEGER NOT NULL,
  permission_id VARCHAR(255) NOT NULL,
  created BIGIN<PERSON>,
  updated BIGIN<PERSON>,
  deleted BIGINT,
  creator_id INTEGER,
  CONSTRAINT uniq_group_perm UNIQUE(group_id,permission_id),
  PRIMARY KEY (id)
);

CREATE TABLE jubiq_sessions(
  id VARCHAR(255) NOT NULL,
  user_id INTEGER,
  user_email VARCHAR(255),
  expires_in INTEGER,
  grant_type VARCHAR(255),
  permissions MEDIUMTEXT,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

--rollback DROP TABLE users,groups,permissions, groups_permissions;