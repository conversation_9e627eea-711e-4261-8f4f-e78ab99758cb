package com.jubiq.loreal.common.endpoints.errorhandlers;

import com.jubiq.loreal.common.exceptions.ErrorMessage;
import com.jubiq.loreal.common.exceptions.JubiqException;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 12/18/15.
 */
public class JubiqExceptionHandler extends JubiqExceptionMapper<JubiqException> {

    @Override
    protected ErrorMessage getErrorMessage(JubiqException e) {
        ErrorMessage message = new ErrorMessage();
        message.errorCode = e.getErrorCode();
        message.errorMessage = e.getMessage();
        return message;
    }

    @Override
    protected Response.Status getStatus(JubiqException e) {
        return e.getStatus();
    }
}
