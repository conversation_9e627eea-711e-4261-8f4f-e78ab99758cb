pipeline {
    agent { label 'agent-manager-node' }

    environment {
        REGISTRY = '************:5000'
        IMAGE_NAME = 'loreal-backend'
        IMAGE_TAG = 'v1.0'
        FULL_IMAGE = "${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    }

    stages {
        stage('Clean workspace') {
            steps {
                deleteDir()
            }
        }

        stage('Checkout Source') {
            steps {
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: '*/dev']],
                    userRemoteConfigs: [[
                        url: 'https://github.com/xoandaica/loreal-http-api',
                        credentialsId: '68361c26-1e7f-4028-bd72-eab616eb7105'
                    ]]
                ])
            }
        }

        stage('Build Java App') {
            steps {
                sh 'mvn clean package -DskipTests'
            }
        }

        stage('Build & Tag Docker Image') {
            steps {
                script {
                    docker.build("${IMAGE_NAME}:${IMAGE_TAG}")
                    sh "docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${FULL_IMAGE}"
                }
            }
        }

        stage('Push Docker Image') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'e75b3c5e-9207-40d5-adfd-3e1822bc3a37',
                    usernameVariable: 'REG_USER',
                    passwordVariable: 'REG_PASS')]) {
                    sh """
                        docker login ${REGISTRY} -u $REG_USER -p $REG_PASS
                        docker push ${FULL_IMAGE}
                    """
                }
            }
        }

        stage('Deploy to Swarm') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'e75b3c5e-9207-40d5-adfd-3e1822bc3a37',
                    usernameVariable: 'REG_USER',
                    passwordVariable: 'REG_PASS')]) {
                    sh """
                        docker login ${REGISTRY} -u $REG_USER -p $REG_PASS
                        docker service update --image ${FULL_IMAGE} --with-registry-auth --force stag_loreal-backend
                    """
                }
            }
        }
    }
}
