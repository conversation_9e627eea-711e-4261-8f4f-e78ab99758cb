--liquibase formatted sql
--changeset vietnq:014
DROP TABLE IF EXISTS notifications,individual_products;
CREATE TABLE notifications (
  id INTEGER NOT NULL,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  presentation_type VARCHAR(255),
  status VARCHAR(255),
  brand_id INTEGER,
  brand_name VARCHAR(255),
  product_range_id INTEGER,
  product_range_name VARCHAR(255),
  product_type_id INTEGER,
  product_type_desc MEDIUMTEXT,
  aw_files TEXT,
  launch_time DATE,
  shipment_request_time DATE,
  intended_use TEXT,
  validated_time DATE,
  cfs_requesting_date DATE,
  cfs_receiving_date DATE,
  cfs_available BOOLEAN,
  cfs_files TEXT,
  inci_requesting_date DATE,
  inci_receiving_date DATE,
  inci_available BOOLEAN,
  inci_files TEXT,
  dav_notification_number TEXT,
  dav_receiving_date DATE,
  dav_expiring_date DATE,
  assignee_id INTEGER,
  assignee_email VARCHAR(255),
  assignee_full_name <PERSON><PERSON><PERSON><PERSON>(255),
  follower_id INTEGER,
  follower_email VARCHAR(255),
  follower_full_name VARCHAR(255),
  creator_email VARCHAR(255),
  creator_full_name VARCHAR(255),
  PRIMARY KEY (id)
);

CREATE TABLE presentation_details (
  id INTEGER NOT NULL,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  notification_id INTEGER,
  product_code VARCHAR(255),
  formula_number VARCHAR(255),
  fil_code VARCHAR(255),
  ingredients TEXT,
  composite_number VARCHAR(255),
  net_weight VARCHAR(255),
  volume VARCHAR(255),
  shade_name VARCHAR(255),
  shade_code VARCHAR(255),
  intended_use TEXT,
  manufacturer_id INTEGER,
  manufacturer_name VARCHAR(255),
  manufacturer_address MEDIUMTEXT,
  manufacturer_phone_number VARCHAR(255),
  manufacturer_fax_number VARCHAR(255),
  manufacturer_country_id INTEGER,
  assembler_id INTEGER,
  assembler_name VARCHAR(255),
  assembler_address MEDIUMTEXT,
  assembler_phone_number VARCHAR(255),
  assembler_fax_number VARCHAR(255),
  assembler_country_id INTEGER,
  assembler_type VARCHAR(255),
  PRIMARY KEY (id)
);

CREATE TABLE ingredients(
  id INTEGER NOT NULL,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  formula_number VARCHAR(255),
  fil_code VARCHAR(255),
  order_number INTEGER,
  full_name VARCHAR(255),
  percentage TEXT,
  PRIMARY KEY (id)
);
-- --rollback;