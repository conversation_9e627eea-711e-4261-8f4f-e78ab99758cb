package com.jubiq.loreal.test;

import com.jubiq.loreal.common.persistence.DaoFactory;
import liquibase.Liquibase;
import liquibase.exception.LiquibaseException;
import org.junit.After;
import org.junit.Before;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by vietnq2 on 11/18/15.
 */
public abstract class JubiqAbstractTest {
    protected static Logger LOGGER = LoggerFactory.getLogger(JubiqAbstractTest.class);

    protected DBI dbi;

    protected Handle handle;

    protected Liquibase liquibase;

    protected DaoFactory daoFactory;

    @Before
    public void setup() throws LiquibaseException {
        dbi = new DBI("***************************************", "loreal", "loreal");
        handle = dbi.open();
        DaoFactory daoFactory = new DaoFactory(dbi);

        try {
            initData();
        } catch (LiquibaseException e) {
            LOGGER.error("Exception init database connection", e);
            throw e;
        }
    }

    @After
    public void tearDown() throws Exception {
        liquibase.dropAll();
        handle.close();
    }

    public abstract void initData() throws LiquibaseException;

}
