package com.jubiq.loreal.test.common;

import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;

import static org.junit.Assert.*;

import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.test.JubiqAbstractTest;
import liquibase.Liquibase;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.LiquibaseException;
import liquibase.resource.ClassLoaderResourceAccessor;
import org.junit.Test;

import java.io.IOException;

/**
 * Created by vietnq2 on 11/18/15.
 */
public class TestCrudDao extends JubiqAbstractTest {

    private JubiqDao<Long,TestLongId> longIdDao;
    private JubiqDao<String,TestStringId> stringIdDao;

    @Override
    public void initData() throws LiquibaseException {
        DaoFactory daoFactory = new DaoFactory(dbi);
        this.longIdDao = daoFactory.createDao(TestLongId.class, Long.class);
        this.stringIdDao = daoFactory.createDao(TestStringId.class, String.class);
        liquibase = new Liquibase("test_basic_crud.xml", new ClassLoaderResourceAccessor(), new JdbcConnection(handle.getConnection()));
        liquibase.update("test");
    }

    @Test
    public void testInsert() throws JubiqPersistenceException, IOException {
        TestLongId objectLong = new TestLongId();
        objectLong = longIdDao.create(objectLong);
        LOGGER.info("created object with id: {}", objectLong.id);
        assertNotNull(longIdDao.get(objectLong.id));

        TestStringId objectString = new TestStringId();
        objectString = stringIdDao.create(objectString);
        LOGGER.info("created object with id: {}", objectString.id);
        assertNotNull(stringIdDao.get(objectString.id));
    }

    @Test
    public void testUpdate() throws JubiqPersistenceException {
        TestLongId object = new TestLongId();
        longIdDao.create(object);

        TestLongId foundObject = longIdDao.get(object.id);
        foundObject.fullName = "updated fn";
        foundObject.address.street = "new street";
        foundObject.relations.get(0).age = 100;
        foundObject.jobType = JobType.DOCTOR;

        longIdDao.update(foundObject);

        foundObject = longIdDao.get(object.id);

        assertEquals(foundObject.fullName,"updated fn");
        assertEquals(foundObject.address.street,"new street");
        assertEquals(foundObject.relations.get(0).age,100);
        assertEquals(foundObject.jobType,JobType.DOCTOR);
        LOGGER.info("updated time: {}",foundObject.updated);
        assertTrue(foundObject.updated > 0);
    }

    @Test
    public void testDelete() throws JubiqPersistenceException {
        TestLongId object = new TestLongId();
        longIdDao.create(object);
        longIdDao.delete(object.id);
        Boolean notFound = false;
        try {
          longIdDao.get(object.id);
        } catch(EntityNotFoundException e) {
          notFound = true;
        }
        assertTrue(notFound);
    }
}
