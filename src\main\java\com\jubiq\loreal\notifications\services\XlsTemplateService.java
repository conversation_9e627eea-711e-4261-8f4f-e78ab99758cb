package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.XlsTemplate;

/**
 * Created by vietnq on 1/23/16.
 */
@Singleton
public class XlsTemplateService extends JubiqService<Long,XlsTemplate>{
    @Inject
    public XlsTemplateService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(XlsTemplate.class,Long.class);
    }
}
