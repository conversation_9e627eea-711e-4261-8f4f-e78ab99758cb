package com.jubiq.loreal.common.model;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.Serializable;

/**
 * Created by vietnq on 11/15/15.
 */
public class JubiqEntity<ID extends Serializable> {
    public ID id;
    public Long created;
    public Long updated;
    public Long deleted;
    public Long creatorId;

    @Override
    public String toString() {
        ObjectMapper jsonMapper = new ObjectMapper();

        try {
            return jsonMapper.writeValueAsString(this);
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }
}
