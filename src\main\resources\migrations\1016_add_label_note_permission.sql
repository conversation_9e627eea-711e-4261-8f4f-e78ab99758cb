--liquibase formatted sql
--changeset thanhlx:1016
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELNOTE:CREATE','Allow to create label note',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELNOTE:FIND','Allow to search for label note',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELNOTE:READ','Allow to view label note',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELNOTE:DELETE','Allow to delete label note',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELNOTE:UPDATE','Allow to update label note',1447653450436,NULL,NULL);

INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELXLSTEMPLATE:CREATE','Allow to create xls template for label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELXLSTEMPLATE:FIND','Allow to search xls template for label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELXLSTEMPLATE:READ','Allow to view xls template for label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELXLSTEMPLATE:DELETE','Allow to delete xls template for label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABELXLSTEMPLATE:UPDATE','Allow to update xls template for label',1447653450436,NULL,NULL);
--rollback;