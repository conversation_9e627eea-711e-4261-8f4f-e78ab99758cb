package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 11/18/15.
 */
public class EntityNotFoundException extends JubiqException {


    public EntityNotFoundException(Throwable e) {
        super(e);
    }

    public EntityNotFoundException(String message) {
        super(message);
    }

    public EntityNotFoundException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.ENTITY_NOT_FOUND.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.ENTITY_NOT_FOUND.name();
    }
}
