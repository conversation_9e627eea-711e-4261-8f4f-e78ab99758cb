## Database settings.
database:
#
#  # the name of your JDBC driver
  driverClass: com.mysql.jdbc.Driver
#
#  # the username
  user: root
#
#  # the password
  password: 
#
#  # the JDBC URL
  url: **********************************

  properties:
    useUnicode: yes
    characterEncoding: UTF-8

# use the simple server factory if you only want to run on a single port
#server:
#  type: simple
#  connector:
#    type: http
#    port: 8080

server:
#  softNofileLimit: 1000
#  hardNofileLimit: 1000
  applicationConnectors:
    - type: http
      port: 3005
#    - type: https
#      port: 8443
#      keyStorePath: example.keystore
#      keyStorePassword: example
#      validateCerts: false
# this requires the alpn-boot library on the JVM's boot classpath
#    - type: spdy3
#      port: 8445
#      keyStorePath: example.keystore
#      keyStorePassword: example
#      validateCerts: false
  adminConnectors:
    - type: http
      port: 8091
#    - type: https
#      port: 8444
#      keyStorePath: example.keystore
#      keyStorePassword: example
#      validateCerts: false
  registerDefaultExceptionMappers: false

# Logging settings.
logging:

  # The default level of all loggers. Can be OFF, ERROR, WARN, INFO, DEBUG, TRACE, or ALL.
  level: INFO

  # Logger-specific levels.
  loggers:

    # Sets the level for 'com.example.app' to DEBUG.
    com.jubiq.loreal: INFO

    # Redirects SQL logs to a separate file
    org.hibernate.SQL:
      level: DEBUG

# Logback's Time Based Rolling Policy - archivedLogFilenamePattern: /tmp/application-%d{yyyy-MM-dd}.log.gz
# Logback's Size and Time Based Rolling Policy -  archivedLogFilenamePattern: /tmp/application-%d{yyyy-MM-dd}-%i.log.gz
# Logback's Fixed Window Rolling Policy -  archivedLogFilenamePattern: /tmp/application-%i.log.gz

  appenders:
    - type: console
    - type: file
      threshold: INFO
      logFormat: "%-6level [%d{HH:mm:ss.SSS}] [%t] %logger{5} - %X{code} %msg %n"
      currentLogFilename: /tmp/application.log
      archivedLogFilenamePattern: /tmp/application-%d{yyyy-MM-dd}-%i.log.gz
      archivedFileCount: 7
      timeZone: UTC
      maxFileSize: 10MB

# the key needs to match the suffix of the renderer
#viewRendererConfiguration:
#    .ftl:
#        strict_syntax: yes
#        whitespace_stripping: yes

#metrics:
#  type: graphite
#  frequency: 10s
swagger:
  resourcePackage: com.jubiq.loreal.umgr.endpoints,com.jubiq.loreal.notifications.endpoints,com.jubiq.loreal.common.endpoints,com.jubiq.loreal.advertisings.endpoints

mailConfiguration:
  username: <EMAIL>
  password: YpqujSEx7r33KFTq
  from: <EMAIL>
  ssl: true
  tls: true
  auth: true
  host: smtp.yandex.com
  port: 465

fileConfiguration:
  baseFolder: D:\xampp\htdocs\loreal\backend\loreal-http-api
serverUrl: http://demo.jubiq.vn:8090/api/