package com.jubiq.loreal.notifications.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * Created by vietnq2 on 11/25/15.
 */
public class Factory extends JubiqEntity<Long> {
    @NotEmpty
    public String name;
    @NotEmpty
    public String address;
    @NotEmpty
    public String phoneNumber;
    @NotEmpty
    public String faxNumber;
    @NotNull
    public Long countryId;
    public String countryName;
    public Integer status;
}
