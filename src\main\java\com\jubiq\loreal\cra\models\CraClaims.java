package com.jubiq.loreal.cra.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Created by AI Assistant on 7/11/25.
 * Model for CRA Claims management
 * Updated to match new database schema
 */
public class CraClaims extends JubiqEntity<Long> {

    @NotNull
    public Long craId; // Reference to cra_request (renamed from craRequestId to match DB)

    public String claimType; // e.g., text, image

    @NotEmpty
    public String claims; // Combined claim content (text or reference to image)

    public String framedRisk; // e.g., IIB, IIIB, IC

    public String criticalRisk; // e.g., IIIC, ID, IID, IIID

    public Long fineAndPenaltyId; // Reference to fine_and_penalty

    public String mktAcceptedStatus; // e.g., accepted, not_accepted

    public Date mktAcceptedDate; // Date when marketing accepted

    public Long mktAcceptedBy; // User ID who accepted from marketing

    public String approvalStatus; //approved, not_approved, pending_gm, pending_cm

    public Date approvalDate; // Date of approval

    public Long approverId; // User ID who approved

    public String approverType; // e.g., MKT, GM, CM

    public String detail; // Additional detail information

    public Long robustnessId; // Reference to robustness
}
