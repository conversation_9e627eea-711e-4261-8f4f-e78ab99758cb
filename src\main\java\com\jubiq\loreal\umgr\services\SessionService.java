package com.jubiq.loreal.umgr.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;

/**
 * Created by vietnq2 on 11/24/15.
 */
@Singleton
public class SessionService extends JubiqService<String,JubiqSession> {
    @Inject
    public SessionService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
        this.dao = this.daoFactory.createDao(JubiqSession.class,String.class);
    }
}
