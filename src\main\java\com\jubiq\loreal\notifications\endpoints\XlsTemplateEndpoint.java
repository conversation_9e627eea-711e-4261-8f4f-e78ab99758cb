package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.notifications.models.XlsTemplate;
import com.jubiq.loreal.notifications.services.XlsTemplateService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/23/16.
 */
@Path("/api/xls-templates")
@Api("Export Templates")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class XlsTemplateEndpoint extends JubiqEndpoint<Long,XlsTemplate> {
    @Inject
    public XlsTemplateEndpoint(XlsTemplateService xlsTemplateService) {
        this.service = xlsTemplateService;
    }
}
