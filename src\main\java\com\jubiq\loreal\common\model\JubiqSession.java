package com.jubiq.loreal.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jubiq.loreal.common.util.NotAColumn;

import java.security.Principal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by vietnq on 11/21/15.
 */
public class JubiqSession extends JubiqEntity<String> implements Principal {

    public Long userId;
    @JsonIgnore
    public String grantType;
    public Integer expiresIn;
    public List<String> permissions;
    public String userEmail;
    public String fullName;
    public String groupName;
    public Long groupId;

    @JsonIgnore
    public Long getCreated() {
        return created;
    }

    @JsonIgnore
    public Long getUpdated() {
        return updated;
    }

    @JsonIgnore
    public Long getCreatorId() {
        return creatorId;
    }

    @JsonIgnore
    public Long getDeleted() {
        return deleted;
    }

    @JsonIgnore
    public Boolean isExpired()  {
        if (System.currentTimeMillis() > (created + (expiresIn * 1000L))) {
            return true;
        }
        return false;
    }

    @Override
    public boolean equals(Object another) {
        return super.equals(another);
    }

    @Override
    public String toString() {
        return super.toString();
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    @JsonIgnore
    public String getName() {
        return this.userEmail;
    }
}
