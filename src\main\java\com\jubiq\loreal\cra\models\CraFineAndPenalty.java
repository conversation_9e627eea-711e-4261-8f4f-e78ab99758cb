package com.jubiq.loreal.cra.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by HH on 7/7/25.
 * Model for Fine and Penalty management
 */
public class CraFineAndPenalty extends JubiqEntity<Long> {
    
    public Integer displayOrder; // For STT/ordering
    
    @NotEmpty
    public String itemName; // Mục hiển thị
    
    public String descriptionOfActs; // Mô tả hành vi vi phạm
    
    public String penaltiesSanctionsApplied; // Chế tài áp dụng
    
    public String otherRemedies; // <PERSON><PERSON><PERSON> thức xử phạt bổ sung/biện pháp khắc phục hậu quả
    
    public String legalBackground; // Điều khoản trích dẫn
}
