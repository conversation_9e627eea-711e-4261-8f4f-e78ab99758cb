package com.jubiq.loreal.labels.endpoints;

import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.labels.models.LabelXlsTemplate;
import com.jubiq.loreal.labels.services.LabelService;
import com.jubiq.loreal.labels.services.LabelService;
import com.jubiq.loreal.labels.services.LabelXlsTemplateService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by THANHLX on 5/17/2018.
 */
@Path("/api/labels")
@Api("labels")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class LabelEndpoint extends JubiqEndpoint<Long,Label> {
    private static Logger LOGGER = LoggerFactory.getLogger(LabelEndpoint.class);

    protected LabelService labelService;
    private LabelXlsTemplateService labelXlsTemplateService;

    @Inject
    public LabelEndpoint(LabelService service, LabelXlsTemplateService labelXlsTemplateService) {
        this.service = this.labelService = service;
        this.labelXlsTemplateService = labelXlsTemplateService;
    }

    @POST
    @Path("/{id}/submit")
    @ApiOperation("Submit a label")
    public Response submit(@Auth JubiqSession session, @PathParam("id") Long id) {
        labelService.submit(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/reject")
    @ApiOperation("Reject a label")
    public Response reject(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        labelService.reject(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/logistic-reject")
    @ApiOperation("Reject a label")
    public Response logisticReject(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        labelService.logisticReject(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/validate")
    @ApiOperation("Validate a label")
    public Response validate(@Auth JubiqSession session, @PathParam("id") Long id) {
        labelService.validate(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/logistic-validate")
    @ApiOperation("Logistic validate a label")
    public Response logisticValidate(@Auth JubiqSession session, @PathParam("id") Long id) {
        labelService.logisticValidate(id, session);
        return Response.ok().build();
    }

    @GET
    @Path("/query-notification")
    @ApiOperation("Query label by notification id")
    public Response queryNotification(@Auth JubiqSession session, @QueryParam("notificationId") Long notificationId) {
        List<Label> labels = labelService.findByNotificationId(notificationId);
        return Response.ok(labels).build();
    }

    @GET
    @Path("/{id}/export")
    @ApiOperation("Export a label ")
    public Response doExportToDavForm(@PathParam("id") Long id) throws IOException {

        File file = labelService.export(id);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=export_" + id + ".xls");
        return builder.build();
    }

    @POST
    @Path("/{id}/recall")
    @ApiOperation("Recall a label")
    public Response doRecall(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        labelService.recall(id, reason, session);
        return Response.ok().build();
    }

    @GET
    @Path("/xls")
    @ApiOperation("Get xls files exported of notifications")

    public Response doExportToXls(@Auth JubiqSession session,
                                  @DefaultValue("") @QueryParam("query") String query,
                                  @DefaultValue(DEFAULT_ORDER) @QueryParam("order") String order,
                                  @DefaultValue("") @QueryParam("filter") String filter,
                                  @QueryParam("fields") String fields,
                                  @QueryParam("templateId") Long templateId) throws IOException {

        if(filter.length() > 0) {
            if(query == null || query.length() == 0) {
                query = filterService.get(filter).query;
            }
            else{
                query += " AND " + filterService.get(filter).query;
            }
        }
        List<Label> notifications = this.labelService.search(query,10000,0,order);
        List<String> fieldNames;
        String sheetName;
        if(templateId != null) {
            LabelXlsTemplate template = labelXlsTemplateService.get(templateId);
            fieldNames = template.fieldNames;
            sheetName = template.name;
        } else {
            fieldNames = Arrays.asList(fields.split(","));
            sheetName = "Labels";
        }
        File file = this.labelService.exportXls(sheetName, notifications,fieldNames,session.userId);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=labels.xls");
        return builder.build();
    }
}
