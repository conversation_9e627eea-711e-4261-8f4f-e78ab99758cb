package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.ProductType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/5/16.
 */
@Singleton
public class ProductTypeService extends JubiqService<Long,ProductType> {
    @Inject
    public ProductTypeService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(ProductType.class,Long.class);
    }

    /*
    @Override
    public void afterUpdate(Long id,ProductType entity) throws JubiqPersistenceException {
        String sql = "update notifications set product_type_desc=:description,updated=:updated where product_type_id=:productTypeId";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("description",entity.description);
        map.put("productTypeId",entity.id);
        this.dao.executeUpdate(sql,map);
    }
    */
    
    public ProductType findByDesc(String description) {
        String sql = "select * from product_types where description like '%" + description + "%' and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        List<ProductType> types = this.dao.search(sql,map);
        if(types != null && types.size() > 0) {
            return types.get(0);
        } else {
            throw new EntityNotFoundException("No product type with description: {}" + description);
        }
    }
}
