package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Note;
import com.jubiq.loreal.notifications.services.NoteService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq2 on 1/27/16.
 */
@Path("/api/notes")
@Api("Notes")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class NoteEndpoint extends JubiqEndpoint<Long,Note> {
    private NoteService noteService;

    @Inject
    public NoteEndpoint(NoteService noteService) {
        this.service = this.noteService = noteService;
    }

    @GET
    @Path("/query")
    @ApiOperation("Query notes by notification")
    public Response findByNotification(@Auth JubiqSession session,@QueryParam("notificationId") Long notificationId) {
        List<Note> notes = this.noteService.findByNotification(notificationId);
        return Response.ok(notes).build();
    }
}
