package com.jubiq.loreal.common.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidOwnerResourceException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.model.PartialUpdate;
import com.jubiq.loreal.notifications.models.Filter;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.services.FilterService;
import com.jubiq.loreal.common.service.JubiqService;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.validation.Valid;
import javax.ws.rs.DELETE;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.List;

/**
 * Created by vietnq on 11/24/15.
 */
public class JubiqEndpoint<ID extends Serializable,T extends JubiqEntity<ID>> {
    public static final String DEFAULT_PAGE_SIZE = "500";
    public static final String DEFAULT_PAGE = "1";
    protected static final int MAX_RESULTS = 500;
    protected static final String DEFAULT_ORDER = "created desc";

    protected static final String SUPER_PERMISSION = "*:*";

    protected JubiqService<ID,T> service;
    protected FilterService filterService;
    protected String create_permission;
    protected String read_permission;
    protected String update_permission;
    protected String delete_permission;
    protected String find_permission;
    protected String PERMISSION_PREFIX;

    public JubiqEndpoint() {
        Class<T> entityClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        PERMISSION_PREFIX = "API:" + entityClass.getSimpleName().toUpperCase() + ":";
        create_permission = PERMISSION_PREFIX + "CREATE";
        update_permission = PERMISSION_PREFIX + "UPDATE";
        read_permission = PERMISSION_PREFIX + "READ";
        delete_permission = PERMISSION_PREFIX + "DELETE";
        find_permission = PERMISSION_PREFIX + "FIND";
    }

    @Inject
    public void setFilterService(FilterService filterService) {
        this.filterService = filterService;
    }

    @GET
    @ApiOperation("Get all entities")
    public Response doFind(@Auth JubiqSession session,
                           @DefaultValue(DEFAULT_PAGE) @QueryParam("page") int page,
                           @DefaultValue(DEFAULT_PAGE_SIZE) @QueryParam("size") int pageSize,
                           @DefaultValue("") @QueryParam("query") String query,
                           @DefaultValue("") @QueryParam("filter") String filterId,
                           @DefaultValue(DEFAULT_ORDER) @QueryParam("order") String order) throws JubiqPersistenceException, UnauthorizedException, InvalidRequestException, EntityNotFoundException {
        if(page < 1) {
            throw new InvalidRequestException("Page should be greater than 0");
        }
        authorize(session,find_permission);
        int limit = pageSize > MAX_RESULTS ? MAX_RESULTS : pageSize;
        int offset = (page - 1) * pageSize;

        if(filterId.length() > 0) {
            Filter filter = filterService.get(filterId);
            if(filter == null) {
               throw new EntityNotFoundException("No filter with id " + filterId);
            }
            if(query == null || query.length() == 0) {
              query = filter.query;
            }
            else{
              query = filter.query + " AND " + query;
            }
        }

        List<T> list = service.search(query,limit,offset,order);
        return Response.ok(list).build();
    }

    @GET
    @Path("/{id}")
    @ApiOperation("Get entity by id")
    public Response doRead(@Auth JubiqSession session, @PathParam("id") ID id) throws JubiqPersistenceException, UnauthorizedException {
        authorize(session, read_permission);
        T entity = service.get(id);
        if(entity != null) {
            return Response.ok(entity).build();
        } else {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
    }

    @POST
    @ApiOperation("Create entity")
    public Response doCreate(@Auth JubiqSession session, @ApiParam @Valid T entity) throws UnauthorizedException, JubiqPersistenceException {
        authorize(session,create_permission);
        entity.creatorId = session.userId;
        T createdEntity = service.create(entity, session);
        return Response.ok(createdEntity).build();
    }

    @PUT
    @Path("/{id}")
    @ApiOperation("Update an entity")
    public Response doUpdate(@Auth JubiqSession session, @PathParam("id") ID id, @ApiParam T entity) throws UnauthorizedException, JubiqPersistenceException, EntityNotFoundException {
        authorize(session, update_permission);
        service.update(id,entity, session);
        return Response.ok().build();
    }

    @DELETE
    @Path("/{id}")
    @ApiOperation("Delete an entity")
    public Response doDelete(@Auth JubiqSession session, @PathParam("id") ID id) throws UnauthorizedException, JubiqPersistenceException {
        authorize(session,delete_permission);
        service.delete(id, session);
        return Response.ok().build();
    }

    @GET
    @Path("/count")
    public int count(@DefaultValue("") @QueryParam("query") String query,
                     @DefaultValue("") @QueryParam("filter") String filterId) throws JubiqPersistenceException, EntityNotFoundException {
        if(filterId.length() > 0) {
          Filter filter = filterService.get(filterId);
          if(filter == null) {
            throw new EntityNotFoundException("No filter with id " + filterId);
          }
          if(query == null || query.length() == 0) {
            query = filter.query;
          }
          else{
            query += " AND " + filter.query;
          }
        }
        return service.count(query);
    }

    @DELETE
    @Path("/batch")
    public Response doBatchDelete(List<ID> ids) throws JubiqPersistenceException {
        service.batchDelete(ids);
        return Response.ok().build();
    }

    @PUT
    @Path("/{id}/partial")
    @ApiOperation("Partial update an entity")
    public Response doPartialUpdate(@Auth JubiqSession session, @PathParam("id") ID id, @ApiParam PartialUpdate partialUpdate) throws UnauthorizedException, JubiqPersistenceException, EntityNotFoundException {
        authorize(session, update_permission);
        service.updateFields(id, partialUpdate.updatedParts, session.userId);
        return Response.ok().build();
    }

    protected void authorize(JubiqSession session, String permission) throws UnauthorizedException {
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.permissions.contains(permission)) {
            throw new UnauthorizedException("Unauthorized call, permission: " + permission);
        }
    }

    protected void checkOwner(JubiqSession session, Long userId) throws InvalidOwnerResourceException {
        if(!session.userId.equals(userId) && !session.permissions.contains(SUPER_PERMISSION)) {
            throw new InvalidOwnerResourceException(userId + " is not allowed to access resources of user " + session.userId);
        }
    }

    protected void checkCurrentUser(Long callerId, Long userId) throws InvalidOwnerResourceException {
        if(!callerId.equals(userId)) {
            throw new InvalidOwnerResourceException(userId + " is not allowed to access resources of user " + callerId);
        }
    }
}
