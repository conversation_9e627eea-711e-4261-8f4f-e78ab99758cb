package com.jubiq.loreal.common.persistence;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.labels.dao.LabelDao;
import com.jubiq.loreal.notifications.dao.NotificationDao;
import com.jubiq.loreal.umgr.dao.UserDao;
import org.skife.jdbi.v2.DBI;

import java.io.Serializable;

/**
 * Created by vietnq on 11/15/15.
 */
@Singleton
public class DaoFactory {

    private DBI dbi;
    private NotificationDao notificationDao;
    private UserDao userDao;
    private LabelDao labelDao;

    @Inject
    public DaoFactory(DBI dbi) {
        this.dbi = dbi;
        this.notificationDao = new NotificationDao(dbi);
        this.userDao = new UserDao(dbi);
        this.labelDao = new LabelDao(dbi);
    }

    public <ID extends Serializable, T extends JubiqEntity<ID>> JubiqDao<ID,T> createDao(Class<T> entityClass, Class<ID> idType) {
        return new JubiqDao<ID, T>(this.dbi, entityClass,idType);
    }


    public NotificationDao getNotificationDao() {
        return notificationDao;
    }

    public UserDao getUserDao(){
      return userDao;
    }

    public LabelDao getLabelDao(){
        return labelDao;
    }
}
