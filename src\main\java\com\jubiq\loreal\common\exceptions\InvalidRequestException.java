package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 12/21/15.
 */
public class InvalidRequestException extends JubiqException {
    public InvalidRequestException(Throwable e) {
        super(e);
    }

    public InvalidRequestException(String message) {
        super(message);
    }

    public InvalidRequestException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.INVALID_REQUEST.name();
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.INVALID_REQUEST.httpStatus;
    }
}
