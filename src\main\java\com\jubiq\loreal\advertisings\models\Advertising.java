package com.jubiq.loreal.advertisings.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.models.PresentationDetail;
import com.jubiq.loreal.notifications.util.Utils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Created by vietnq on 3/7/16.
 */
public class Advertising extends JubiqEntity<Long> {
    public AdStatus status;
    public Long assigneeId;
    public String assigneeEmail;
    public String assigneeFullName;
    public Long followerId;
    public String followerFullName;
    public String followerEmail;
    public String creatorEmail;
    public String creatorFullName;
    public Long groupId;
    public Boolean validated;
    public List<Integer> requestIds;
    public String advertisingMediaSelection;
    public String description;
    @Serialized
    public Map<String,String> contentFiles;
    @Serialized
    public Map<String,String> referencesFiles;
    public List<KeyvisualMessage> messages;
    public Date validatedTime;
    public Date licenseRequestingDate;
    public Date licenseReceivingDate;
    public String licenseNumber;
    public Date submittedDate;
    public Date licenseExpiredDate;
    @Serialized
    public Map<String,String> licenseFiles;
    @Serialized
    public Map<String,String> otherFiles;
    public Long brandId;
    public String brandName;
    public Date revisingDate;
    @Serialized
    public Map<String,String> proofDocuments;

    public String advertisementDetail="";
    public String channel = "";
    public String valueOtherChannel="";
    public String timeline = "";
    public String advertisementType = "KV";

    public Set<String> compareFields(Advertising other) {
        Set<String> differentFields = new HashSet<>();
        Class<?> clazz = getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value1 = field.get(this);
                Object value2 = field.get(other);
                if(value1 == null && value2 == null) continue;
                if(field.getName().equals("assigneeId")
                        || field.getName().equals("brandId")
                        || field.getName().equals("groupId")
                        || field.getName().equals("status")
                        || field.getName().equals("assigneeFullName")
                        || field.getName().equals("followerId")
                        || field.getName().equals("followerFullName")
                        || field.getName().equals("creatorFullName")
                        || field.getName().equals("validated")
                        || field.getName().equals("messages")
                        || field.getName().equals("validatedTime")
                        || field.getName().equals("licenseRequestingDate")
                        || field.getName().equals("licenseReceivingDate")
                        || field.getName().equals("submittedDate")
                        || field.getName().equals("licenseExpiredDate")
                        || field.getName().equals("brandName")
                        || field.getName().equals("creatorEmail")
                        || field.getName().equals("revisingDate")

                ) continue;
                if(field.getName().equals("requestIds")){
                    List<Integer> stringSplit1 = (List<Integer>)value1;
                    List<Integer> stringSplit2 = (List<Integer>)value2;
                    if(!stringSplit1.containsAll(stringSplit2) || !stringSplit2.containsAll(stringSplit1)) differentFields.add("Requests");
                    continue;
                }
                if(value1 instanceof Map && value2 instanceof Map && (((Map)value1).equals((Map)value2))){
                    continue;
                }
                if(value1 instanceof Date && value2 instanceof Date && Utils.convertDate((Date)value1).equals(Utils.convertDate((Date)value2))){
                    continue;
                }
                if (value1 != null && value2 == null || value1 == null && value2 != null || !value1.equals(value2)) {

                    differentFields.add(Utils.lowerCaseToCamelCase(getFieldName(field.getName())));

                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return differentFields;
    }

    public String getFieldName(String field){
        String fieldReturn = field;
        switch (field){
            case "productName":
                fieldReturn = "Tên sản phẩm";
                break;
            case "brandName":
                fieldReturn = "Nhãn hàng";
                break;
            case "userManual":
                fieldReturn = "Hướng dẫn sử dụng";
                break;
            case "distributor":
                fieldReturn = "Phân phối bởi";
                break;
            case "lotNumber":
                fieldReturn = "Số lô";
                break;
            case "expirationDate":
                fieldReturn = "Ngày hết hạn";
                break;
            case "colorIngredients":
                fieldReturn = "Thành phần của màu trang điểm";
                break;
            case "baseIngredients":
                fieldReturn = "Thành phần dòng sản phẩm";
                break;
            case "ingredients":
                fieldReturn = "Thành phần";
                break;
            case "manufacturer":
                fieldReturn = "Nước sản xuất";
                break;
            case "other":
                fieldReturn = "Thông tin khác";
                break;
            case "importer":
                fieldReturn = "Chịu trách nhiệm đưa sản phẩm ra thị trường";
                break;
            case "alarm":
                fieldReturn = "Cảnh báo";
                break;
            case "manufacturingDate":
                fieldReturn = "Ngày sản xuất";
                break;
            case "hotline":
                fieldReturn = "Số điên thoại tư vấn khách hàng";
                break;
            case "davNotificationNumber":
                fieldReturn = "Số công bố";
                break;
            case "preservation":
                fieldReturn = "Bảo quản";
                break;
            case "uses":
                fieldReturn = "Công dụng, hiệu quả";
                break;
        }
        return fieldReturn;
    }
}
