package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.notifications.models.report.BrandReport;
import com.jubiq.loreal.notifications.models.report.CfsReport;
import com.jubiq.loreal.notifications.models.report.NotificationReport;

import java.util.*;

/**
 * Created by vietnq on 2/28/16.
 */
@Singleton
public class ReportService {
    private NotificationService notificationService;
    private BrandService brandService;
    @Inject
    public ReportService(NotificationService notificationService,BrandService brandService) {
        this.notificationService = notificationService;
        this.brandService = brandService;
    }

    public NotificationReport notificationReport(Date startDate, Date endDate) {
        String baseSql = "select count(*) from notifications where deleted is null and submitting_date is not null";

        String sql = baseSql + " and submitting_date >= :startDate and submitting_date <= :endDate";

        Map<String,Object> map = new HashMap<String,Object>();
        map.put("startDate", startDate);
        map.put("endDate", endDate);

        Long so_yeu_cau_moi = notificationService.getDao().selectLong(sql,map);

        sql = baseSql + " and status = 'COMPLETED' and dav_receiving_date >= :startDate and dav_receiving_date <= :endDate";
        map = new HashMap<String,Object>();
        map.put("startDate",startDate);
        map.put("endDate",endDate);

        Long so_yeu_cau_xu_ly_xong = notificationService.getDao().selectLong(sql,map);

        sql = baseSql + " and ((status != 'COMPLETED' and dav_receiving_date is null) OR (status = 'COMPLETED' and dav_receiving_date >= :startDate)) and submitting_date < :startDate";
        map = new HashMap<String, Object>();
        map.put("startDate",startDate);

        Long so_yeu_cau_doi_xu_ly_truoc = notificationService.getDao().selectLong(sql,map);


        sql = baseSql + " and ((status != 'COMPLETED' and dav_receiving_date is null) OR (status = 'COMPLETED' and dav_receiving_date > :endDate)) and submitting_date <= :endDate";
        map = new HashMap<String, Object>();
        map.put("endDate",endDate);

        Long so_yeu_cau_doi_xu_ly_sau = notificationService.getDao().selectLong(sql,map);

        NotificationReport report = new NotificationReport();
        report.so_yeu_cau_moi = so_yeu_cau_moi;
        report.so_yeu_cau_da_xu_ly = so_yeu_cau_xu_ly_xong;
        report.so_yeu_cau_doi_xu_ly_sau = so_yeu_cau_doi_xu_ly_sau;
        report.so_yeu_cau_doi_xu_ly_truoc = so_yeu_cau_doi_xu_ly_truoc;

        if(so_yeu_cau_xu_ly_xong > 0) {
          sql = "select SUM(UNIX_TIMESTAMP(dav_receiving_date)-UNIX_TIMESTAMP(submitting_date)) from notifications where deleted is null and submitting_date is not null and status = 'COMPLETED' and dav_receiving_date between :startDate and :endDate";
          map = new HashMap<String, Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          Long tong_thoi_gian_xu_ly = notificationService.getDao().selectLong(sql, map);
          double average = tong_thoi_gian_xu_ly / so_yeu_cau_xu_ly_xong;
          report.thoi_gian_xu_ly_trung_binh = average;
        }
        return report;
    }

    public List<CfsReport> cfsReport(Date startDate, Date endDate) {
        String countriesSql = "select manufacturer_country_name from presentation_details group by manufacturer_country_name";
        List<String> countries = new ArrayList<String>();
        countries = notificationService.getDao().selectString(countriesSql);

        String baseSql = "select count(DISTINCT notifications.id, presentation_details.manufacturer_country_name) from notifications left join presentation_details on notifications.id = presentation_details.notification_id where notifications.deleted is null";

        List<CfsReport> result = new ArrayList<CfsReport>();
        for(String countryName : countries) {
          CfsReport report = new CfsReport();

          report.countryName = countryName;

          String sql = baseSql + " and manufacturer_country_name = :countryName and notifications.cfs_requesting_date >= :startDate and notifications.cfs_requesting_date <= :endDate";
          Map<String,Object> map = new HashMap<String,Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          map.put("countryName", countryName);
          report.so_yeu_cau_moi = notificationService.getDao().selectLong(sql,map);

          sql = baseSql + " and manufacturer_country_name = :countryName and cfs_receiving_date >= :startDate and cfs_receiving_date <= :endDate";
          map = new HashMap<String,Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          map.put("countryName", countryName);
          report.so_yeu_cau_da_xu_ly = notificationService.getDao().selectLong(sql,map);

          sql = baseSql + " and manufacturer_country_name = :countryName and (cfs_receiving_date is null OR cfs_receiving_date > :endDate) and cfs_requesting_date <= :endDate";
          map = new HashMap<String,Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          map.put("countryName", countryName);
          report.so_yeu_cau_doi_xu_ly_sau = notificationService.getDao().selectLong(sql,map);

          sql = baseSql + " and manufacturer_country_name = :countryName and (cfs_receiving_date is null OR cfs_receiving_date >= :startDate) and cfs_requesting_date < :startDate";
          map = new HashMap<String,Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          map.put("countryName", countryName);
          report.so_yeu_cau_doi_xu_ly_truoc = notificationService.getDao().selectLong(sql,map);

          if(report.so_yeu_cau_da_xu_ly > 0) {
            sql = "select SUM(UNIX_TIMESTAMP(process_time_table.cfs_receiving_date)-UNIX_TIMESTAMP(process_time_table.cfs_requesting_date)) FROM (select notifications.cfs_receiving_date, notifications.cfs_requesting_date from notifications left join presentation_details on notifications.id = presentation_details.notification_id where notifications.deleted is null";
            sql += " and manufacturer_country_name = :countryName and cfs_receiving_date >= :startDate and cfs_receiving_date <= :endDate";
            sql += " group by notifications.id, presentation_details.manufacturer_country_name) AS process_time_table";
            map = new HashMap<String, Object>();
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            map.put("countryName", countryName);
            Long tong_thoi_gian_xu_ly = notificationService.getDao().selectLong(sql, map);
            double thoi_gian_xu_ly_trung_binh = tong_thoi_gian_xu_ly / report.so_yeu_cau_da_xu_ly;
            report.thoi_gian_xu_ly_trung_binh = thoi_gian_xu_ly_trung_binh;
          }
          result.add(report);
        }
        Collections.sort(result, new Comparator<CfsReport>() {
          @Override
          public int compare(CfsReport o1, CfsReport o2) {
            return o1.countryName.compareTo(o2.countryName);
          }
        });

        return result;
    }

    public List<BrandReport> brandReport(Date startDate,Date endDate) {
        String countriesSql = "select brand_name from notifications group by brand_name";
        List<String> brands = new ArrayList<String>();
        brands = notificationService.getDao().selectString(countriesSql);
        List<BrandReport> result = new ArrayList<BrandReport>();

        String baseSql = "select count(*) from notifications where deleted is null and submitting_date is not null";

        for(String brandName : brands) {
          BrandReport report = new BrandReport();

          report.brandName = brandName;

          String sql = baseSql + " and brand_name = :brandName and submitting_date >= :startDate and submitting_date <= :endDate";

          Map<String,Object> map = new HashMap<String,Object>();
          map.put("startDate", startDate);
          map.put("endDate", endDate);
          map.put("brandName", brandName);

          Long so_yeu_cau_moi = notificationService.getDao().selectLong(sql,map);

          sql = baseSql + " and brand_name = :brandName and status = 'COMPLETED' and dav_receiving_date >= :startDate and dav_receiving_date <= :endDate";
          map = new HashMap<String,Object>();
          map.put("startDate",startDate);
          map.put("endDate",endDate);
          map.put("brandName", brandName);

          Long so_yeu_cau_xu_ly_xong = notificationService.getDao().selectLong(sql,map);

          sql = baseSql + " and brand_name = :brandName and ((status != 'COMPLETED' and dav_receiving_date is null) OR (status = 'COMPLETED' and dav_receiving_date >= :startDate)) and submitting_date < :startDate";
          map = new HashMap<String, Object>();
          map.put("startDate",startDate);
          map.put("brandName", brandName);

          Long so_yeu_cau_doi_xu_ly_truoc = notificationService.getDao().selectLong(sql,map);


          sql = baseSql + " and brand_name = :brandName and ((status != 'COMPLETED' and dav_receiving_date is null) OR (status = 'COMPLETED' and dav_receiving_date > :endDate)) and submitting_date <= :endDate";
          map = new HashMap<String, Object>();
          map.put("endDate",endDate);
          map.put("brandName", brandName);

          Long so_yeu_cau_doi_xu_ly_sau = notificationService.getDao().selectLong(sql,map);

          if(so_yeu_cau_xu_ly_xong > 0) {
            sql = "select SUM(UNIX_TIMESTAMP(dav_receiving_date)-UNIX_TIMESTAMP(submitting_date)) from notifications where deleted is null and submitting_date is not null and brand_name = :brandName and status = 'COMPLETED' and dav_receiving_date between :startDate and :endDate";
            map = new HashMap<String, Object>();
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            map.put("brandName", brandName);
            Long tong_thoi_gian_xu_ly = notificationService.getDao().selectLong(sql, map);
            double thoi_gian_xu_ly_trung_binh = tong_thoi_gian_xu_ly / so_yeu_cau_xu_ly_xong;
            report.thoi_gian_xu_ly_trung_binh = thoi_gian_xu_ly_trung_binh;
          }

          report.so_yeu_cau_moi = so_yeu_cau_moi;
          report.so_yeu_cau_da_xu_ly = so_yeu_cau_xu_ly_xong;
          report.so_yeu_cau_doi_xu_ly_sau = so_yeu_cau_doi_xu_ly_sau;
          report.so_yeu_cau_doi_xu_ly_truoc = so_yeu_cau_doi_xu_ly_truoc;

          result.add(report);
        }

        Collections.sort(result, new Comparator<BrandReport>() {
          @Override
          public int compare(BrandReport o1, BrandReport o2) {
            return o1.brandName.compareTo(o2.brandName);
          }
        });
        return result;
    }
}
