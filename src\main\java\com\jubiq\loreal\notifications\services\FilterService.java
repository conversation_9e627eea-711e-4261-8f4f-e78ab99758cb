package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Filter;
import com.jubiq.loreal.common.persistence.DaoFactory;

import static com.jubiq.loreal.LorealConstants.FILTER_SCOPE_GLOBAL;
import static com.jubiq.loreal.LorealConstants.FILTER_SCOPE_USER;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * Created by vietnq on 12/11/15.
 */
@Singleton
public class FilterService extends JubiqService<String, Filter> {


    @Inject
    public FilterService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(Filter.class, String.class);
    }

    public List<Filter> userFilters(final Long userId) throws JubiqPersistenceException {
        String sql = "select * from filters where scope='GLOBAL' or user_id=:userId and deleted is NULL order by created asc";
        Map<String, Object> map = new HashMap<String, Object>() {{
            put("userId", userId);
        }};
        List<Filter> filterList = this.dao.search(sql, map);

        // sua doi mot phan tham so trong cac query filter truoc khi return cho user
        for (Filter filter : filterList) {
            updateQueryFilter(filter);
        }
        return filterList;
    }


    @Override
    public Filter create(Filter entity, JubiqSession session) throws JubiqPersistenceException {
        beforeCreate(entity);
        return super.create(entity, session);
    }

    @Override
    public void beforeCreate(Filter entity) throws JubiqPersistenceException {
        if (!FILTER_SCOPE_GLOBAL.equals(entity.scope) && !FILTER_SCOPE_USER.equals(entity.scope)) {
            throw new InvalidRequestException("Scope is not valid");
        }
        if (entity.scope.equals(FILTER_SCOPE_USER) && entity.userId == null) {
            throw new InvalidRequestException("User id must be not null for filter with scope USER");
        }
    }

    @Override
    public Filter get(String s) throws JubiqPersistenceException {
        Filter filter = super.get(s);
        updateQueryFilter(filter);
        return filter;
    }

    private void updateQueryFilter(Filter filter) {
        if (filter.id.equals("EXPIRED_IN_1_YEAR")) {
            LocalDate now = LocalDate.now();
            String nextYear = String.valueOf(now.plusYears(2).withDayOfYear(1).withMonth(1));
            String startDate = String.valueOf(now.plusYears(1).withDayOfYear(1).withMonth(1));
            /**
             * -------------- startDate 2023-01-01
             * -------------- end_date 2024-01-01
             */
            filter.query = filter.query.replace("end_date", nextYear);
//            filter.query = filter.query.replace("start_date", String.valueOf(now));
            filter.query = filter.query.replace("start_date", startDate);
        }
    }
    public void executeRenameTitleExpireJob() {
        LocalDate currentDate = LocalDate.now();
        int nextYear = currentDate.getYear() + 1;
        Filter filter = super.get("EXPIRED_IN_1_YEAR");
        filter.name = "Expired in " + nextYear;
        this.dao.update(filter);
    }
}
