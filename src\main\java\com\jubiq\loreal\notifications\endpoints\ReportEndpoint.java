package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.report.BrandReport;
import com.jubiq.loreal.notifications.models.report.CfsReport;
import com.jubiq.loreal.notifications.models.report.NotificationReport;
import com.jubiq.loreal.notifications.services.ReportService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 2/28/16.
 */
@Path("/api/reports")
@Api("reports")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class ReportEndpoint {
    private ReportService reportService;
    @Inject
    public ReportEndpoint(ReportService reportService) {
        this.reportService = reportService;
    }

    @GET
    @Path("/notifications-report")
    @ApiOperation("Get notifications report")
    public Response getNotificationReport(@Auth JubiqSession session,
                                          @QueryParam("startDate") String startDate,
                                          @QueryParam("endDate") String endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        NotificationReport report = reportService.notificationReport(sdf.parse(startDate),sdf.parse(endDate));
        return Response.ok(report).build();
    }

    @GET
    @Path("/cfs-report")
    @ApiOperation("Get cfs report")
    public Response getCfsReport(@Auth JubiqSession session,
                                          @QueryParam("startDate") String startDate,
                                          @QueryParam("endDate") String endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<CfsReport> reports = reportService.cfsReport(sdf.parse(startDate), sdf.parse(endDate));
        return Response.ok(reports).build();
    }

    @GET
    @Path("/brand-report")
    @ApiOperation("Get brand report")
    public Response getBrandReport(@Auth JubiqSession session,
                                 @QueryParam("startDate") String startDate,
                                 @QueryParam("endDate") String endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<BrandReport> reports = reportService.brandReport(sdf.parse(startDate), sdf.parse(endDate));
        return Response.ok(reports).build();
    }
}
