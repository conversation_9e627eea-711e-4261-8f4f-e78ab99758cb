package com.jubiq.loreal.umgr.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jubiq.loreal.common.exceptions.ForgotPasswordTokenExpiredException;
import com.jubiq.loreal.common.exceptions.InvalidForgotPasswordTokenException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import org.hibernate.validator.constraints.NotEmpty;
import org.mindrot.jbcrypt.BCrypt;

import static com.jubiq.loreal.LorealConstants.*;

import javax.validation.constraints.NotNull;

/**
 * Created by vietnq on 11/17/15.
 */
public class User extends JubiqEntity<Long> {

    @NotEmpty
    public String fullName;
    @NotEmpty
    public String email;
    @JsonIgnore
    public String password;
    @NotNull
    public Long groupId;
    public String groupName;
    public String avatarUri;
    @JsonIgnore
    public String forgotPwdToken;
    @JsonIgnore
    public Long forgotPwdTokenRequested;

    private Boolean forgotTokenStillValid() {
        if(forgotPwdToken == null && forgotPwdTokenRequested == null) {
            return false;
        }
        return System.currentTimeMillis() - forgotPwdTokenRequested < FORGOT_PASSWORD_EXPIRED_TIME;
    }

    public void clearForgottenPasswordToken() {
        forgotPwdToken = null;
        forgotPwdTokenRequested = null;
    }

    public Boolean changeForgottenPassword(String newPassword, String token) throws InvalidForgotPasswordTokenException, ForgotPasswordTokenExpiredException {
        if(token == null || !token.equals(forgotPwdToken)) {
            throw new InvalidForgotPasswordTokenException("Invalid token");
        }
        if(forgotTokenStillValid()) {
            password = BCrypt.hashpw(newPassword, BCrypt.gensalt());
        } else {
            throw new ForgotPasswordTokenExpiredException("Token is expired");
        }
        return true;
    }

    public void changePassword(String currentPassword, String newPassword) throws InvalidRequestException {
        if(!BCrypt.checkpw(currentPassword,this.password)) {
            throw new InvalidRequestException("Current password is not correct");
        }
        setEncryptedPassword(newPassword);
    }

    public void setEncryptedPassword(String password) {
        this.password = encryptedPassword(password);
    }

    private String encryptedPassword(String password) {
        return BCrypt.hashpw(password, BCrypt.gensalt());
    }
}
