package com.jubiq.loreal.cra.models;

import com.jubiq.loreal.advertisings.models.KeyvisualMessage;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.notifications.util.Utils;
import org.hibernate.validator.constraints.NotEmpty;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Created by AI Assistant on 7/9/25.
 * Model for CRA Request management
 */
public class CraRequest extends JubiqEntity<Long> {
    
    // Creator information
    public String creatorEmail;
    public String creatorFullName;
    
    // Assignee information
    public Long assigneeId;
    public String assigneeEmail;
    public String assigneeFullName;
    public Long followerId;
    public String followerEmail;
    public String followerFullName;
    public Long groupId;
    public CRAStatus status; // e.g., WAIT_FOR_MKT_SUBMISSION, WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN, etc.
    public List<Integer> requestIds;
    public String expositionLevel;
    public String expositionDetail;
    @Serialized
    public Map<String,String> contentFiles; // text - JSON serialized files
    @Serialized
    public Map<String,String> referencesFiles; // text - JSON serialized files
    public Date timeline;
    public String advertisementType;
    
    // Important dates
    public Date dateOfSubmitting;
    public Date dateOfRequestingProofDocument;
    public Date dateOfReceivingProofDocument;
    public Date dateOfStradRiskAssessment;
    public Date dateOfCompleted;
    
    // Documents and files
    @Serialized
    public Map<String,String> proofDocuments; // text - JSON serialized documents
    @Serialized
    public Map<String,String> stradRiskAssessmentFiles; // text - JSON serialized files
    
    // Communication
    public List<KeyvisualMessage> messages; // text - For chat functionality
    
    // Quick login hashes
    public String gmLoginHash; // For quick login link for GM
    public String cmLoginHash; // For quick login link for CM

    public Set<String> compareFields(CraRequest other) {
        Set<String> differentFields = new HashSet<>();
        Class<?> clazz = getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value1 = field.get(this);
                Object value2 = field.get(other);
                if(value1 == null && value2 == null) continue;

                // Skip fields that shouldn't trigger change notifications
                if(field.getName().equals("assigneeId")
                        || field.getName().equals("assigneeEmail")
                        || field.getName().equals("assigneeFullName")
                        || field.getName().equals("followerId")
                        || field.getName().equals("followerEmail")
                        || field.getName().equals("followerFullName")
                        || field.getName().equals("creatorEmail")
                        || field.getName().equals("creatorFullName")
                        || field.getName().equals("groupId")
                        || field.getName().equals("status")
                        || field.getName().equals("dateOfSubmitting")
                        || field.getName().equals("dateOfRequestingProofDocument")
                        || field.getName().equals("dateOfReceivingProofDocument")
                        || field.getName().equals("dateOfStradRiskAssessment")
                        || field.getName().equals("dateOfCompleted")
                        || field.getName().equals("messages")
                        || field.getName().equals("gmLoginHash")
                        || field.getName().equals("cmLoginHash")
                ) continue;

                // Special handling for requestIds list
                if(field.getName().equals("requestIds")){
                    List<Integer> list1 = (List<Integer>)value1;
                    List<Integer> list2 = (List<Integer>)value2;
                    if(list1 != null && list2 != null) {
                        if(!list1.containsAll(list2) || !list2.containsAll(list1)) {
                            differentFields.add("Request IDs");
                        }
                    } else if(list1 != list2) {
                        differentFields.add("Request IDs");
                    }
                    continue;
                }

                // Special handling for Map fields
                if(value1 instanceof Map && value2 instanceof Map && (((Map)value1).equals((Map)value2))){
                    continue;
                }

                // Special handling for Date fields
                if(value1 instanceof Date && value2 instanceof Date && Utils.convertDate((Date)value1).equals(Utils.convertDate((Date)value2))){
                    continue;
                }

                // Check for differences
                if (value1 != null && value2 == null || value1 == null && value2 != null || !value1.equals(value2)) {
                    differentFields.add(Utils.lowerCaseToCamelCase(getFieldName(field.getName())));
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return differentFields;
    }

    public String getFieldName(String field){
        String fieldReturn = field;
        switch (field){
            case "expositionLevel":
                fieldReturn = "Exposition Level";
                break;
            case "expositionDetail":
                fieldReturn = "Exposition Detail";
                break;
            case "advertisementType":
                fieldReturn = "Advertisement Type";
                break;
            case "timeline":
                fieldReturn = "Timeline";
                break;
            case "contentFiles":
                fieldReturn = "Content Files";
                break;
            case "referencesFiles":
                fieldReturn = "Reference Files";
                break;
            case "proofDocuments":
                fieldReturn = "Proof Documents";
                break;
            case "stradRiskAssessmentFiles":
                fieldReturn = "STRD Risk Assessment Files";
                break;
            default:
                fieldReturn = field;
                break;
        }
        return fieldReturn;
    }
}
