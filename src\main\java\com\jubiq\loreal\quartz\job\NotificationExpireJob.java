package com.jubiq.loreal.quartz.job;

import com.google.inject.Inject;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.models.enumerations.ExportedField;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.notifications.services.ConfigurationService;
import com.jubiq.loreal.notifications.services.ExportService;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.jubiq.loreal.LorealConstants.SCI_MANAGER_GROUP_ID;

public class NotificationExpireJob implements Job {
  @Inject
  private NotificationService notificationService;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) {
    notificationService.executeNotificationExpireJob();
  }
}
