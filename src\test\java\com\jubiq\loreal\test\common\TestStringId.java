package com.jubiq.loreal.test.common;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.Serialized;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by vietnq2 on 11/18/15.
 */
public class TestStringId extends JubiqEntity<String> {
    public String fullName;
    public List<String> skills;
    public List<Relation> relations;
    @Serialized
    public Address address;
    public JobType jobType;
    public Boolean active;
    public Date birthDay;

    public TestStringId() {
        fullName = "Test Object";
        skills = Arrays.asList(new String[]{"java","linux","js"});
        relations = new ArrayList<Relation>();
        relations.add(new Relation("com.jubiq.loreal.test.common.Relation 1",21));
        relations.add(new Relation("com.jubiq.loreal.test.common.Relation 2",22));

        address = new Address("To Hieu",121);
        birthDay = new Date();
        active = true;
        jobType = JobType.ENGINEER;
    }
}
