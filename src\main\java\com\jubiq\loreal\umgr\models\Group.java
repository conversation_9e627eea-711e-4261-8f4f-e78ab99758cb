package com.jubiq.loreal.umgr.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by vietnq on 11/21/15.
 */
public class Group extends JubiqEntity<Long> {
    @NotEmpty
    public String name;
    @NotNull
    public Long ascendantId;

    @NotAColumn
    public List<String> permissions;
}