package com.jubiq.loreal.cra.models;

/**
 * Created by AI Assistant on 7/9/25.
 * Enum for CRA Request exported fields
 */
public enum CraRequestExportedField {
    id("CRA Request No"),
    status("Status"),
    creatorEmail("Creator Email"),
    creatorFull<PERSON>ame("Creator Full Name"),
    assigneeId("Assignee ID"),
    assigneeE<PERSON>("Assignee Email"),
    assigneeFullName("Assignee Full Name"),
    followerId("Follower ID"),
    followerEmail("Follower Email"),
    followerFullName("Follower Full Name"),
    groupId("Group ID"),
    requestIds("Request IDs"),
    expositionLevel("Exposition Level"),
    expositionDetail("Exposition Detail"),
    advertisementType("Advertisement Type"),
    timeline("Timeline"),
    dateOfSubmitting("Date of Submitting"),
    dateOfRequestingProofDocument("Date of Requesting Proof Document"),
    dateOfReceivingProofDocument("Date of Receiving Proof Document"),
    dateOfStradRiskAssessment("Date of STRD Risk Assessment"),
    dateOfCompleted("Date of Completed"),
    contentFiles("Content Files"),
    referenceFiles("Reference Files"),
    proofDocuments("Proof Documents"),
    stradRiskAssessmentFiles("STRD Risk Assessment Files"),
    messages("Messages"),
    gmLoginHash("GM Login Hash"),
    cmLoginHash("CM Login Hash"),
    created("Created Date"),
    updated("Updated Date");

    public String displayName;

    CraRequestExportedField(String displayName) {
        this.displayName = displayName;
    }
}
