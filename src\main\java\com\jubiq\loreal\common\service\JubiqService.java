package com.jubiq.loreal.common.service;

import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.model.JubiqEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 11/19/15.
 */
public class JubiqService<ID extends Serializable, T extends JubiqEntity<ID>> {

    private static Logger LOGGER = LoggerFactory.getLogger(JubiqService.class);

    protected DaoFactory daoFactory;

    protected JubiqDao<ID,T> dao;

    public JubiqDao<ID,T> getDao() {
        return this.dao;
    }

    public List<T> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        return dao.search(query,limit,offset, order);
    }

    public T create(T entity, JubiqSession session) throws JubiqPersistenceException {
        entity.creatorId = session == null ? 1000L : session.userId;
        beforeCreate(entity);

        T createdEntity = dao.create(entity);
        afterCreate(createdEntity);
        return createdEntity;
    }

    public void update(ID id, T entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        beforeUpdate(id,entity);
        dao.update(entity);
        afterUpdate(id,entity);
    }

    public void updateFields(ID id, Map<String, Object> map, Long callerId) throws JubiqPersistenceException {
        this.dao.updateFields(id, map);
        T entity = this.dao.get(id);
        afterUpdate(id,entity);
    }

    public void delete(ID id, JubiqSession session) throws JubiqPersistenceException {
        dao.delete(id);
    }

    public T get(ID id) throws JubiqPersistenceException {
        return dao.get(id);
    }

    public List<T> getAll() throws JubiqPersistenceException {
        return dao.getAll();
    }

    public int count(String whereExp) throws JubiqPersistenceException {
        return dao.count(whereExp);
    }

    public void batchDelete(List<ID> ids) throws JubiqPersistenceException {
        dao.batchDelete(ids);
    }

    public void beforeCreate(T entity) throws JubiqPersistenceException {
        validateEntity(entity);
    }

    protected void afterCreate(T entity) throws JubiqPersistenceException {
    }

    public void beforeUpdate(ID id, T entity) throws JubiqPersistenceException {
        validateEntity(entity);
    }

    public void afterUpdate(ID id, T entity) throws JubiqPersistenceException {
        //override in subclasses
    }
    protected void validateEntity(T entity) {
        //override in subclasses
    }
}
