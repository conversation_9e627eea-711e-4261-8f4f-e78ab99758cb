package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.notifications.models.Filter;
import com.jubiq.loreal.notifications.services.FilterService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/5/16.
 */
@Path("/api/filters")
@Api("filters")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class FilterEndpoint extends JubiqEndpoint<String,Filter> {
    private FilterService filterService;
    @Inject
    public FilterEndpoint(FilterService filterService) {
        this.service = this.filterService = filterService;
    }
}
