package com.jubiq.loreal.common.util;

/**
 * Created by vietnq on 11/17/15.
 */
public class NamingUtil {

    public static String toSnakeCase(String input) {
        if (input==null) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for(int i = 0 ; i < input.length();i++){
            char c = input.charAt(i);
            if (Character.isUpperCase(c)){
                if (i==0) {
                    result.append(Character.toLowerCase(c));
                } else {
                    result.append('_').append(Character.toLowerCase(c));
                }
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
}
