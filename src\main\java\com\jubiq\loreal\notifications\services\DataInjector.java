package com.jubiq.loreal.notifications.services;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.LorealConstants;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.AssemblerType;
import com.jubiq.loreal.notifications.models.Brand;
import com.jubiq.loreal.notifications.models.Country;
import com.jubiq.loreal.notifications.models.Factory;
import com.jubiq.loreal.notifications.models.Ingredient;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.models.PresentationDetail;
import com.jubiq.loreal.notifications.models.ProductType;
import com.jubiq.loreal.notifications.models.Range;
import com.jubiq.loreal.notifications.models.enumerations.ExportedField;
import com.jubiq.loreal.notifications.models.enumerations.NotificationStatus;
import com.jubiq.loreal.notifications.models.enumerations.Presentation;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 2/15/16.
 */
@Singleton
public class DataInjector {
    private static final String ADMIN_EMAIL = "<EMAIL>";
    private static final String SCI_MANAGER_EMAIL = "<EMAIL>";
    private static final Logger logger = LoggerFactory.getLogger(DataInjector.class);
    private NotificationService notificationService;
    private IngredientService ingredientService;
    private BrandService brandService;
    private ProductTypeService productTypeService;
    private FactoryService factoryService;
    private CountryService countryService;
    private UserService userService;
    private RangeService rangeService;
    @Inject
    public DataInjector(NotificationService notificationService,
                        IngredientService ingredientService,
                        BrandService brandService,
                        ProductTypeService productTypeService,
                        FactoryService factoryService,
                        CountryService countryService,
                        UserService userService,
                        RangeService rangeService) {
        this.notificationService = notificationService;
        this.ingredientService = ingredientService;
        this.brandService = brandService;
        this.productTypeService = productTypeService;
        this.factoryService = factoryService;
        this.countryService = countryService;
        this.userService = userService;
        this.rangeService = rangeService;
    }

    public void prepareData() {
//        prepareUsers();
        //import L'OREAL data
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("imported-data/imported-data.xlsx");
            Workbook wb = new XSSFWorkbook(is);
//            importBrand();
//            importCountryAndFactory();
            importProductType();
//            importIngredients(wb);
//            importNotifications(wb);
            wb.close();
            is.close();
        } catch(FileNotFoundException e) {
            logger.error("Cannot find file for imported data");
        } catch(IOException e) {
            logger.error("IOException when importing data");
        }
    }

    private void prepareUsers() {
        try {
            userService.findByEmail(ADMIN_EMAIL);
        } catch(EntityNotFoundException e) {
            User admin = new User();
            admin.email = ADMIN_EMAIL;
            admin.password = "loreal";
            admin.fullName = "Châu Ngọc Võ";
            admin.groupId = 1000L;
            userService.create(admin,null);
        }

        try {
            userService.findByEmail(SCI_MANAGER_EMAIL);
        } catch(EntityNotFoundException e) {
            User sciManager = new User();
            sciManager.email = SCI_MANAGER_EMAIL;
            sciManager.password = "loreal";
            sciManager.fullName = "Nguyễn Thị Huyền";
            sciManager.groupId = 2001L;
            userService.create(sciManager,null);
        }

    }

    private void importNotifications(Workbook wb) {
        logger.info("importing notification ...");
        JubiqSession session = new JubiqSession();
        session.userId = 1000L;
        Map<Long,Notification> notificationMap = new HashMap<Long, Notification>();
        Map<Integer,ExportedField> fieldMap = new HashMap<Integer, ExportedField>();
        Sheet sheet = wb.getSheetAt(0);
        Iterator<Row> iterator = sheet.iterator();
        while(iterator.hasNext()) {
            Row row = iterator.next();
            int rowIndex = row.getRowNum();
            Iterator<Cell> cellIterator = row.iterator();
            if(rowIndex == 0) {
                //read headers
                while(cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    String cellValue = cell.getStringCellValue();
                    for(ExportedField field : ExportedField.values()) {
                        if(field.displayName.equals(cellValue)) {
                            fieldMap.put(cell.getColumnIndex(), field);
                            break;
                        }
                    }
                }
            } else {
                Long id = null;
                Notification notification = new Notification();
                PresentationDetail detail = new PresentationDetail();
                Factory manufacturer = new Factory();
                Factory assembler = new Factory();
                Country manufacturerCountry = new Country();
                Country assemblerCountry = new Country();
                Brand brand = new Brand();
                ProductType productType = new ProductType();
                User creator = new User();
                User follower = new User();
                User assignee = new User();
                Range productRange = new Range();

                while(cellIterator.hasNext()) {

                    Cell cell = cellIterator.next();
                    int columnIndex = cell.getColumnIndex();
                    ExportedField field = fieldMap.get(columnIndex);

                    switch(field) {
                        case id:
                            id = (long) cell.getNumericCellValue();
                            notification.id = id;
                            detail.notificationId = id;
                            break;
                        case presentationType:
                            notification.presentationType = Presentation.valueOf(cell.getStringCellValue());
                            break;
                        case followerEmail:
                            notification.followerEmail = cell.getStringCellValue();
                            follower.email = notification.followerEmail;
                            break;
                        case creatorEmail:
                            notification.creatorEmail = cell.getStringCellValue();
                            creator.email = notification.creatorEmail;
                            break;
                        case brand:
                            String brandName = cell.getStringCellValue();
                            notification.brandName = brandName;
                            brand.name = brandName;
                            break;
                        case productRange:
                            productRange.name = cell.getStringCellValue();
                            notification.productRangeName = productRange.name;
                            break;
                        case productName:
                            notification.productName = cell.getStringCellValue();
                            break;
                        /*
                        case productType:
                            String descEn = cell.getStringCellValue();
                            productType.description = descEn;
                            notification.productTypeDesc = descEn;
                            break;
                        */
                        case launchTime:
                            notification.launchTime = cell.getDateCellValue();
                            break;
                        case shipmentRequestTime:
                            notification.shipmentRequestTime = cell.getDateCellValue();
                            break;
                        case assigneeEmail:
                            notification.assigneeEmail = cell.getStringCellValue();
                            assignee.email = notification.assigneeEmail;
                            break;
                        case validatedTime:
                            Date validatedTime = cell.getDateCellValue();
                            notification.validatedTime = validatedTime;
                            break;
                        case cfsRequestingDate:
                            notification.cfsRequestingDate = cell.getDateCellValue();
                            break;
                        case cfsReceivingDate:
                            notification.cfsReceivingDate = cell.getDateCellValue();
                            notification.cfsAvailable = notification.cfsReceivingDate != null;
                            break;
                        case inciRequestingDate:
                            notification.inciRequestingDate = cell.getDateCellValue();
                            break;
                        case inciReceivingDate:
                            cell.setCellType(Cell.CELL_TYPE_NUMERIC);
                            notification.inciReceivingDate = cell.getDateCellValue();
                            notification.inciAvailable = notification.inciReceivingDate != null;
                            break;
                        case davNotificationNumber:
                            notification.davNotificationNumber = cell.getStringCellValue();
                            notification.davAvailable = notification.davNotificationNumber != null && notification.davNotificationNumber.length() > 0;
                            break;
                        case davReceivingDate:
                            notification.davReceivingDate = cell.getDateCellValue();
                            break;
                        case davExpiringDate:
                            notification.davExpiringDate = cell.getDateCellValue();
                            break;
                        case productCode:
                            detail.productCode = cell.getStringCellValue();
                            break;
                        case formulaNumber:
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            detail.formulaNumber = cell.getStringCellValue();
                            break;
                        case filCode:
                            detail.filCode = cell.getStringCellValue();
                            break;
                        case compositeNumber:
                            detail.compositeNumber = cell.getStringCellValue();
                            break;
                        case netWeight:
                            detail.netWeight = cell.getStringCellValue();
                            break;
                        case volume:
                            detail.volume = cell.getStringCellValue();
                            break;
                        case shadeName:
                            detail.shadeName = cell.getStringCellValue();
                            break;
                        case shadeCode:
                            detail.shadeCode = cell.getStringCellValue();
                            break;
                        case intendedUse:
                            notification.intendedUse = cell.getStringCellValue();
                            break;
                        case individualIntendedUse:
                            detail.individualIntendedUse = cell.getStringCellValue();
                            break;
                        case manufacturer:
                            manufacturer.name = cell.getStringCellValue();
                            detail.manufacturerName = manufacturer.name;
                            break;
                        case manufacturerCountry:
                            manufacturer.countryName = cell.getStringCellValue();
                            manufacturerCountry.name = manufacturer.countryName;
                            detail.manufacturerCountryName = manufacturer.countryName;
                            break;
                        case manufacturerAddress:
                            manufacturer.address = cell.getStringCellValue();
                            detail.manufacturerAddress = manufacturer.address;
                            break;
                        case assembler:
                            assembler.name = cell.getStringCellValue();
                            detail.assemblerName = assembler.name;
                            break;
                        case assemblerCountry:
                            assembler.countryName = cell.getStringCellValue();
                            assemblerCountry.name = assembler.countryName;
                            detail.assemblerCountryName = assembler.countryName;
                            break;
                        case assemblerAddress:
                            assembler.address = cell.getStringCellValue();
                            detail.assemblerAddress = assembler.address;
                            break;
                        case assemblerType:
                            try {
                                detail.assemblerType = AssemblerType.valueOf(cell.getStringCellValue().toUpperCase());
                            } catch (Exception e) {
                                logger.error("cannot get assembler type of {} in row {}",cell.getStringCellValue(),row.getRowNum());
                                continue;
                            }
                            break;
                        case requestDate:
                            Date requestDate = cell.getDateCellValue();
                            notification.created = requestDate.getTime();
                            break;
                        default:
                            break;
                    }
                }

                if(detail.assemblerType == null) {
                    logger.info("no assembler type",row.getRowNum());
                    continue;
                }

                if(brand.name == null || brand.name.length() == 0) {
                    logger.info("no brand name in row: {}",row.getRowNum());
                    continue;
                }
                if(manufacturer.countryName == null || manufacturer.countryName.length() == 0) {
                    logger.info("no manufacturer country name in row: {}",row.getRowNum());
                    continue;
                }

                if(assembler.countryName == null || assembler.countryName.length() == 0) {
                    logger.info("no assembler country name in row: {}",row.getRowNum());
                    continue;
                }

                if(productType.description == null || productType.description.length() == 0) {
                    logger.info("no product type description in row: {}",row.getRowNum());
                    continue;
                }

                if(notification.creatorEmail == null) {
                    logger.info("no creator email in row: {}",row.getRowNum());
                    continue;
                }

                if(notification.followerEmail == null) {
                    logger.info("no creator email in row: {}",row.getRowNum());
                    continue;
                }
                if(notification.launchTime == null) {
                    logger.info("no launch time in row: {}",row.getRowNum());
                    continue;
                }

                notification.followerEmail = notification.followerEmail.toLowerCase();
                notification.creatorEmail = notification.creatorEmail.toLowerCase();

                try {
                    follower = userService.findByEmail(notification.followerEmail);
                } catch(EntityNotFoundException e) {
                    follower.fullName = notification.followerEmail;
                    follower.password = "loreal";
                    follower.groupId = LorealConstants.MARKETING_GROUP_ID;
                    follower = userService.create(follower,session);
                }

                notification.followerId = follower.id;
                notification.followerFullName = follower.fullName;

                try {
                    creator = userService.findByEmail(notification.creatorEmail);
                } catch(EntityNotFoundException e) {
                    creator.fullName = notification.creatorEmail;
                    creator.password = "loreal";
                    creator.groupId = LorealConstants.MARKETING_GROUP_ID;
                    creator = userService.create(creator,session);
                }

                notification.creatorId = creator.id;
                notification.creatorFullName = creator.fullName;

                if(notification.assigneeEmail != null) {
                    notification.assigneeEmail = notification.assigneeEmail.toLowerCase();
                    try {
                        assignee = userService.findByEmail(notification.assigneeEmail);
                    } catch(EntityNotFoundException e) {
                        assignee.fullName = notification.assigneeEmail;
                        assignee.password = "loreal";
                        assignee.groupId = LorealConstants.SCI_STAFF_GROUP_ID;
                        assignee = userService.create(assignee,session);
                    }

                    notification.assigneeId = assignee.id;
                    notification.assigneeFullName = assignee.fullName;
                }

                try {
                    brand = brandService.findByName(notification.brandName);
                } catch(EntityNotFoundException e) {
                    brand = brandService.create(brand,session);
                }
                notification.brandId = brand.id;

                productRange.brandId = brand.id;


                /*
                try {
                    productRange = rangeService.findByNameAndBrand(productRange.name, productRange.brandId);
                } catch(EntityNotFoundException e) {
                    productRange = rangeService.create(productRange,null);
                }

                notification.productRangeId = productRange.id;
                */
                try {
                    productType = productTypeService.findByDesc(notification.productTypeDesc);
                } catch(EntityNotFoundException e) {
                    productType = productTypeService.create(productType,session);
                }
                //notification.productTypeId = productType.id;
                notification.productTypeIds = new ArrayList<Long>();
                notification.productTypeIds.add(productType.id);

                try {
                    manufacturerCountry = countryService.findByName(manufacturerCountry.name);
                } catch(EntityNotFoundException e) {
                    manufacturerCountry = countryService.create(manufacturerCountry,session);
                }
                manufacturer.countryId = manufacturerCountry.id;

                try {
                    assemblerCountry = countryService.findByName(assemblerCountry.name);
                } catch(EntityNotFoundException e) {
                    assemblerCountry = countryService.create(assemblerCountry,session);
                }
                assembler.countryId = assemblerCountry.id;

                try {
                    manufacturer = factoryService.findByName(manufacturer.name);
                } catch(EntityNotFoundException e) {
                    manufacturer = factoryService.create(manufacturer,session);
                }
                detail.manufacturerId = manufacturer.id;

                try {
                    assembler = factoryService.findByName(assembler.name);
                } catch(EntityNotFoundException e) {
                    assembler = factoryService.create(assembler,session);
                }
                detail.assemblerId = assembler.id;

                detail.ingredients = ingredientService.findByFLAandFIL(detail.formulaNumber,detail.filCode);

                if(notification.validatedTime == null) {
                    notification.validatedTime = new Date(notification.created);
                }
                notification.validated = true;
                notification.shipmentRequestTime = (notification.shipmentRequestTime == null) ? notification.launchTime : notification.shipmentRequestTime;

                if(notification.davNotificationNumber !=null  && notification.davNotificationNumber.length() > 0) {
                    notification.status = NotificationStatus.COMPLETED;
                } else if(notification.davRequestingDate != null) {
                    notification.status = NotificationStatus.WAIT_FOR_NOTIFICATION_NUMBER;
                } else if(notification.cfsReceivingDate != null && notification.inciReceivingDate != null) {
                    notification.status = NotificationStatus.WAIT_FOR_SUBMISSION;
                } else if(notification.cfsReceivingDate != null && notification.inciReceivingDate == null) {
                    notification.status = NotificationStatus.WAIT_FOR_INCI;
                } else if(notification.cfsReceivingDate == null && notification.inciReceivingDate != null) {
                    notification.status = NotificationStatus.WAIT_FOR_CFS;
                } else {
                    notification.status = NotificationStatus.WAIT_FOR_CFS_INCI;
                }

                if(id != null) {
                    if(notificationMap.get(id) == null) {
                        notification.presentationDetails.add(detail);
                        notificationMap.put(id,notification);
                    } else {
                        notificationMap.get(id).presentationDetails.add(detail);
                    }
                }
            }
        }

        logger.info("there are {} notifications",notificationMap.size());

        for(Notification notification : notificationMap.values()) {
            logger.info("importing notification #{} ...",notification.id);
            if(notification.presentationDetails.size() == 0) {
                logger.info("no presentation detail for notification #{}",notification.id);
                continue;
            }
            JubiqSession creatorSession = new JubiqSession();
            creatorSession.fullName = notification.creatorEmail;
            creatorSession.userId = notification.creatorId;
            try {
                notificationService.get(notification.id);
                logger.info("notification #{} already existed",notification.id);
            } catch(EntityNotFoundException e) {
                //only create if not existed
                notificationService.create(notification,creatorSession);
            }
        }
    }

    private void importIngredients(Workbook wb) {
        logger.info("importing ingredients ....");
        int count = 0;
        Sheet sheet = wb.getSheetAt(1);
        Map<Integer,String> fieldMap = new HashMap<Integer, String>();
        Iterator<Row> rowIterator = sheet.iterator();
        while(rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Iterator<Cell> cellIterator = row.iterator();
            if(row.getRowNum() == 0) {
                while(cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    fieldMap.put(cell.getColumnIndex(),cell.getStringCellValue());
                }
            } else {
                Ingredient ingredient = new Ingredient();
                while(cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    String fieldName = fieldMap.get(cell.getColumnIndex());
                    if("Formula Number".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.formulaNumber = cell.getStringCellValue();
                    } else if("Filcode".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.filCode = cell.getStringCellValue();
                    } else if("No".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_NUMERIC);
                        ingredient.orderNumber = (int)cell.getNumericCellValue();
                    } else if("Full Ingredient name".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.fullName = cell.getStringCellValue();
                    } else if("Percentages of restricted ingredients".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.percentage = cell.getStringCellValue();
                    }
                }
                try {
                    ingredientService.findByPK(ingredient.formulaNumber,ingredient.filCode,ingredient.fullName);
                    logger.info("ingredient with fla: {}, filCode: {}, name: {} already existed",
                            ingredient.formulaNumber,ingredient.filCode,ingredient.fullName);
                } catch(EntityNotFoundException e) {
                    ingredientService.create(ingredient,null);
                    count ++;
                }
             }
        }
        logger.info("imported {} ingredients",count);
    }

    private void importBrand() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("imported-data/Import_brand.xlsx");
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            JubiqSession session = new JubiqSession();
            session.userId = 1000L;
            while(rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String brandName = row.cellIterator().next().getStringCellValue();
                try {
                    Brand brand = brandService.findByName(brandName);
                    logger.info("brand {} already existed",brandName);
                } catch(EntityNotFoundException e) {
                    Brand brand = new Brand();
                    brand.name = brandName;
                    brandService.create(brand,session);
                }
            }
            wb.close();
            is.close();
        } catch(FileNotFoundException e) {
            logger.error("Cannot find file for imported brand");
        } catch(IOException e) {
            logger.error("IOException when importing brand");
        }
    }

    private void importProductType() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("imported-data/Import_product_types.xlsx");
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            JubiqSession session = new JubiqSession();
            session.userId = 1000L;
            while(rowIterator.hasNext()) {
                Row row = rowIterator.next();
                if (row.getCell(0) != null){
                    String desc = row.getCell(0).getStringCellValue();
                    row.getCell(1).setCellType(Cell.CELL_TYPE_STRING);
                    Integer davCode = Integer.valueOf(row.getCell(1).getStringCellValue());
                    if(desc != null && desc.length() > 0) {
                        try {
                            desc = desc.trim();
                            ProductType productType = productTypeService.findByDesc(desc);
                            logger.info("description {} already existed",desc);
                            productType.davCode = Integer.valueOf(davCode);
                            productTypeService.update(productType.id, productType, session);
                        } catch(EntityNotFoundException e) {
                            logger.info("Create new ProductType desc= {}, davCode={}", desc, davCode);
                            ProductType type = new ProductType();
                            type.description = desc;
                            type.davCode = Integer.valueOf(davCode);
                            productTypeService.create(type,session);
                        }
                    }
                }
            }
            wb.close();
            is.close();
        } catch(FileNotFoundException e) {
            logger.error("Cannot find file for imported product type");
        } catch(IOException e) {
            logger.error("IOException when importing product type");
        }
    }
    private void updateExistedDavCodeForProductType(){

    }

    private void importCountryAndFactory() {
        try {
            List<Factory> factories = new ArrayList<Factory>();
            InputStream is = getClass().getClassLoader().getResourceAsStream("imported-data/Import_country_factory.xlsx");
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            JubiqSession session = new JubiqSession();
            session.userId = 1000L;
            Map<Integer,String> fieldMap = new HashMap<Integer, String>();
            while(rowIterator.hasNext()) {
                Factory factory = new Factory();
                Row row = rowIterator.next();
                Iterator<Cell> cellIterator = row.iterator();
                while(cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    if(row.getRowNum() == 0) {
                        fieldMap.put(cell.getColumnIndex(),cell.getStringCellValue());
                    } else {
                        String fieldName = fieldMap.get(cell.getColumnIndex());
                        if("Country".equals(fieldName)) {
                            factory.countryName = cell.getStringCellValue();
                        } else if("Factory".equals(fieldName)) {
                            factory.name = cell.getStringCellValue();
                        } else if("Address".equals(fieldName)) {
                            factory.address = cell.getStringCellValue();
                        } else if("Phone".equals(fieldName)) {
                            factory.phoneNumber = cell.getStringCellValue();
                        } else if("Fax".equals(fieldName)) {
                            factory.faxNumber = cell.getStringCellValue();
                        }
                    }
                }
                if(factory.name != null && factory.name.length() > 0) {
                    factories.add(factory);
                }
            }

            for(Factory factory : factories) {
                try {
                    factory = factoryService.findByName(factory.name);
                    logger.info("factory {} already existed",factory.name);
                } catch(EntityNotFoundException e1) {
                    try {
                        Country country = countryService.findByName(factory.countryName);
                        factory.countryId = country.id;
                    } catch(EntityNotFoundException e2) {
                        Country country = new Country();
                        country.name = factory.countryName;
                        country = countryService.create(country,session);
                        factory.countryId = country.id;
                    }
                    factoryService.create(factory, session);
                }
            }
            wb.close();
            is.close();
        } catch(FileNotFoundException e) {
            logger.error("Cannot find file for imported country factory");
        } catch(IOException e) {
            logger.error("IOException when importing country factory");
        }
    }
}
