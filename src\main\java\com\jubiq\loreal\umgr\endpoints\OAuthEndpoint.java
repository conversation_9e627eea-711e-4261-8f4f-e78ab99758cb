package com.jubiq.loreal.umgr.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.umgr.services.UserService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.AuthenticationException;

import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;

/**
 * Created by vietnq on 11/22/15.
 */
@Path("/api/oauth")
@Api("oauth")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class OAuthEndpoint {

    private UserService userService;

    @Inject
    public OAuthEndpoint(UserService userService) {
        this.userService = userService;
    }

    @POST
    @Path("/token")
    @ApiOperation("Get access token")
    public Response requestAccessToken(@FormParam("client_id") String clientId,
                                            @FormParam("client_secret") String clientSecret) throws JubiqPersistenceException, AuthenticationException, EntityNotFoundException {

        JubiqSession session = userService.createUserSession(clientId,clientSecret);
        return Response.ok(session).build();
    }
}
