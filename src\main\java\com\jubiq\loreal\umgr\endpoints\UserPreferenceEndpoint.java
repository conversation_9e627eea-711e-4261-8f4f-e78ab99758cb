package com.jubiq.loreal.umgr.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.umgr.models.UserPreference;
import com.jubiq.loreal.umgr.services.UserPreferenceService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq2 on 12/15/15.
 */
@Path("/api/preferences")
@Api("user preferences")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class UserPreferenceEndpoint extends JubiqEndpoint<Long, UserPreference> {
    @Inject
    public UserPreferenceEndpoint(UserPreferenceService preferenceService) {
        this.service = preferenceService;
    }
}
