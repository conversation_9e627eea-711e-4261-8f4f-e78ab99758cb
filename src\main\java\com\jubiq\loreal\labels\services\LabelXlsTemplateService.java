package com.jubiq.loreal.labels.services;

import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.labels.models.LabelXlsTemplate;
import javax.inject.Inject;
import com.google.inject.Singleton;

@Singleton
public class LabelXlsTemplateService extends JubiqService<Long,LabelXlsTemplate> {
    @Inject
    public LabelXlsTemplateService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(LabelXlsTemplate.class,Long.class);
    }
}