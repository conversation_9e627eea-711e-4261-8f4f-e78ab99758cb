package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqFile;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Ingredient;
import com.jubiq.loreal.notifications.services.IngredientService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

import java.io.IOException;
import java.io.InputStream;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import static javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;

/**
 * Created by vietnq2 on 1/29/16.
 */
@Path("/api/ingredients")
@Api("Ingredients")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED,MULTIPART_FORM_DATA})
@Singleton
public class IngredientEndpoint extends JubiqEndpoint<Long,Ingredient> {
    private IngredientService ingredientService;
    @Inject
    public IngredientEndpoint(IngredientService ingredientService) {
        this.service = this.ingredientService = ingredientService;
    }

    @POST
    @Path("/import")
    @ApiOperation("Import ingredient from uploaded file")
    public Response uploadFile(@Auth JubiqSession session,
                               @ApiParam @FormDataParam("file") InputStream uploadedInputStream,
                               @ApiParam @FormDataParam("file") FormDataContentDisposition fileDetail) throws IOException, JubiqPersistenceException, EntityNotFoundException {
        ingredientService.importIngredient(uploadedInputStream,session);
        return Response.ok().build();
    }
}
