<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-2.0.xsd">

    <include file="migrations/001_umgr_schema.sql" />
    <include file="migrations/002_notification_schema.sql" />
    <include file="migrations/003_filter_schema.sql" />
    <include file="migrations/004_filter_schema.sql" />
    <include file="migrations/005.sql" />
    <include file="migrations/006.sql" />
    <include file="migrations/007.sql" />
    <include file="migrations/008_change_notification_structure.sql" />
    <include file="migrations/009_multi_file_upload.sql" />
    <include file="migrations/010_add_column_notification_composite_number.sql" />
    <include file="migrations/011_add_column_individual_products.sql" />
    <include file="migrations/012.sql" />
    <include file="migrations/013.sql" />
    <include file="migrations/014_change_notification_schema.sql" />
    <include file="migrations/015.sql" />
    <include file="migrations/016.sql" />
    <include file="migrations/017.sql" />
    <include file="migrations/018_add_auto_increment_notification.sql" />
    <include file="migrations/019_change_column_name_details.sql" />
    <include file="migrations/020_history.sql" />
    <include file="migrations/021_update_notification_history.sql" />
    <include file="migrations/022_dav_requesting_date_notification.sql" />
    <include file="migrations/023_update_user_table.sql" />
    <include file="migrations/024_alerts.sql" />
    <include file="migrations/025.sql" />
    <include file="migrations/026_update_session.sql" />
    <include file="migrations/027_filter_preference.sql" />
    <include file="migrations/028_xls_template.sql" />
    <include file="migrations/029_notes.sql" />
    <include file="migrations/030_update_notes.sql" />
    <include file="migrations/031_update_notes.sql" />
    <include file="migrations/1000_init_umgr_data.sql" />
    <include file="migrations/1001.sql" />
    <include file="migrations/1002.sql" />
    <include file="migrations/1003.sql" />
    <include file="migrations/1004.sql" />
    <include file="migrations/1005.sql" />
    <include file="migrations/1006.sql" />
    <include file="migrations/1007_new_permissions.sql" />
    <include file="migrations/1008_new_permissions.sql" />
    <include file="migrations/1009_new_permissions.sql" />
    <include file="migrations/1010_new_permissions.sql" />
    <include file="migrations/1011_permission_and_special_session.sql" />
    <include file="migrations/032_update_notification.sql" />
    <include file="migrations/1012_new_group.sql" />
    <include file="migrations/033_advertising_schema.sql" />
    <include file="migrations/1013_sci_staff_assign_permission.sql" />
    <include file="migrations/034_new_warning_column.sql" />
    <include file="migrations/035_new_individual_warning_column.sql" />
    <include file="migrations/036_add_auto_increment_ingredients.sql" />
    <include file="migrations/037_add_index_for_notification_id.sql" />
    <include file="migrations/038_add_column_notification_launch_form_files.sql" />
    <include file="migrations/1014_add_logistics_group.sql" />
    <include file="migrations/039_add_column_notification_reject_product_name.sql" />
    <include file="migrations/040_add_product_name_validation_filter.sql" />
    <include file="migrations/041_add_columns_common_shade_name_and_product_type_and_composite.sql" />
    <include file="migrations/042_add_filter_wait_for_sc_validation.sql" />
    <include file="migrations/043_label_schema.sql" />
    <include file="migrations/044_add_selected_label_fields_preference.sql" />
    <include file="migrations/045_add_label_email_template.sql" />
    <include file="migrations/046_update_label_schema.sql" />
    <include file="migrations/047_add_recall_label_email_template.sql" />
    <include file="migrations/048_add_column_dav_official_requesting_date.sql" />
    <include file="migrations/1015_add_label_permissions.sql" />
    <include file="migrations/2000_quartz_chema.sql" />
    <include file="migrations/2001_add_email_template.sql" />
    <include file="migrations/2002_add_column_discontinued.sql" />
    <include file="migrations/049_update_schema_labels.sql" />
    <include file="migrations/050_add_label_tables.sql" />
    <include file="migrations/1016_add_label_note_permission.sql" />
    <include file="migrations/2003_add_expired_cfs_inci_email_template.sql" />
    <include file="migrations/2004_update_reject_email_template.sql" />
    <include file="migrations/2005_add_column_discontinued_date.sql" />
    <include file="migrations/051_change_wait_for_sci_submission_to_wait_for_mkt_submission.sql" />
    <include file="migrations/2006_add_column_exporter_and_bar_code.sql" />
    <include file="migrations/2007_update_advertising_table.sql" />
    <include file="migrations/2008_update_dav_form.sql" />
    <include file="migrations/2009_update_label_advertising_process_date.sql" />
    <include file="migrations/2010_update_advertising.sql" />
    <include file="migrations/2011_add_discontinued_filter.sql" />
    <include file="migrations/2011_add_discontinued_filter.sql" />
    <include file="migrations/2012_add_label_filter_and_advertising_filter.sql" />
    <include file="migrations/052_test" />
</databaseChangeLog>