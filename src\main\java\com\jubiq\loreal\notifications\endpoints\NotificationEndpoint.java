package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Ingredient;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.models.XlsTemplate;
import com.jubiq.loreal.notifications.models.enumerations.ProcessAction;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.notifications.services.XlsTemplateService;
import com.jubiq.loreal.umgr.services.UserService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 11/29/15.
 */
@Path("/api/notifications")
@Api("notifications")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class NotificationEndpoint extends JubiqEndpoint<Long,Notification> {
    private static Logger LOGGER = LoggerFactory.getLogger(NotificationEndpoint.class);

    protected NotificationService notificationService;
    private XlsTemplateService xlsTemplateService;
    private UserService userService;

    @Inject
    public NotificationEndpoint(NotificationService service, XlsTemplateService xlsTemplateService, UserService userService) {
        this.service = this.notificationService = service;
        this.xlsTemplateService = xlsTemplateService;
        this.userService = userService;
    }

    @POST
    @Path("/add")
    @ApiOperation("Add old notification")
    public Response doAdd(@Auth JubiqSession session, @ApiParam Notification entity) {
      Notification notification = this.notificationService.add(entity, session);
      return Response.ok(notification).build();
    }

    @POST
    @Path("/{id}/split")
    @ApiOperation("Split notification")
    public Response doSplit(@Auth JubiqSession session, @PathParam("id") Long id) {
      Notification notification = this.notificationService.split(id, session);
      return Response.ok(notification).build();
    }

    @POST
    @Path("/{id}/split-all")
    @ApiOperation("Split all notification")
    public Response doSplitAll(@Auth JubiqSession session,  @PathParam("id") Long id) {
        this.notificationService.splitAll(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/split-sku")
    @ApiOperation("Split notification with selected sku")
    public Response doSplitSku(@Auth JubiqSession session, @PathParam("id") Long id, @QueryParam("skus") String skus) {
        Notification notification = this.notificationService.splitSku(id, skus, session);
        return Response.ok(notification).build();
    }

    @POST
    @Path("/{id}/submit")
    @ApiOperation("submit a notification")
    public Response doSubmit(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.submitToSci(id,session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/medical-device-defined")
    @ApiOperation("Classification medical device")
    public Response doMedicalDeviceDefined(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.medicalDeviceDefined(id,session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/recall")
    @ApiOperation("recall a notification")
    public Response doRecall(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        notificationService.recall(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/renotify")
    @ApiOperation("renotify a notification")
    public Response renotify(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        notificationService.renotify(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/revise")
    @ApiOperation("Revise a notification")
    public Response revise(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.revise(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/cancel")
    @ApiOperation("Cancel a notification")
    public Response cancel(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        notificationService.cancel(id,reason,session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/reject")
    @ApiOperation("Reject a notification")
    public Response reject(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        notificationService.reject(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/validate-product-name")
    @ApiOperation("Validate english product name")
    public Response validateProductName(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.validateProductName(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/reject-product-name")
    @ApiOperation("Reject english product name")
    public Response rejectProductName(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        notificationService.rejectProductName(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/update-product-name")
    @ApiOperation("Update english product name")
    public Response updateProductName(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.updateProductName(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/validate")
    @ApiOperation("Validate a notification")
    public Response validate(@Auth JubiqSession session, @PathParam("id") Long id) {
        notificationService.validate(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/assign")
    @ApiOperation("Assign a notification")
    public Response assign(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("assigneeId") Long assigneeId) {
        authorize(session,"API:NOTIFICATION:ASSIGN");
        notificationService.assign(id,assigneeId,session);
        return Response.ok().build();
    }

    @POST
    @Path("/transfer-follower")
    @ApiOperation("Transfer notifications to a new follower")
    public Response transferFollower(@Auth JubiqSession session, @FormParam("oldFollowerId") Long oldFollowerId, @FormParam("newFollowerId") Long newFollowerId) {
        authorize(session,"API:NOTIFICATION:TRANSFER");
        notificationService.transferFollower(oldFollowerId,newFollowerId,session.userId);
        return Response.ok().build();
    }

    /**
     * change follower by requestId
     * @param session
     * @param newFollowerId
     * @return
     */
    @POST
    @Path("/transfer-follower-by-query")
    @ApiOperation("Transfer notifications to a new follower by notificationId")
    public Response changeFollower(@Auth JubiqSession session,
                                   @FormParam("selectedItems") String selectedItems,
                                   @FormParam("newFollowerId") Long newFollowerId,
                                   @FormParam("query") String query,
                                   @FormParam("changeAll") Boolean changeAll,
                                   @FormParam("changeSelected") Boolean changeSelected,
                                   @FormParam("filter") String filter
                                   ) {
        authorize(session,"API:NOTIFICATION:TRANSFER");
        notificationService.transferFollower(session,selectedItems, query, changeAll, changeSelected,newFollowerId, filter);
        return Response.ok().build();
    }

    @POST
    @Path("/transfer-assignee")
    @ApiOperation("Transfer notifications to a new follower")
    public Response transferAssignee(@Auth JubiqSession session, @FormParam("oldAssigneeId") Long oldAssigneeId, @FormParam("newAssigneeId") Long newAssigneeId) {
        authorize(session,"API:NOTIFICATION:TRANSFER");
        notificationService.transferAssignee(oldAssigneeId,newAssigneeId,session.userId);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/cfs-inci-dav")
    @ApiOperation("Process cfs/inci/dav")
    public Response doProcessInciCfsDav(@Auth JubiqSession session, @PathParam("id") Long id, ProcessInfo info) {
        Notification notification = notificationService.getDao().get(id);
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this notification");
        }

        ProcessAction action = ProcessAction.valueOf(info.action);

        switch(action) {
            case REQUEST_CFS:
                notificationService.requestCfs(id,info.date,session);
                break;
            case RESET_REQUEST_CFS:
                notificationService.resetRequestCfs(id,info.date,session);
                break;
            case REQUEST_INCI:
                notificationService.requestInci(id,info.date,session);
                break;
            case RESET_REQUEST_INCI:
                notificationService.resetRequestInci(id,info.date,session);
                break;
            case REQUEST_DAV:
                notificationService.requestDav(id,info.date,session);
                break;
            case RECEIVE_CFS:
                notificationService.receiveCfs(id,info.date,info.files,session);
                break;
            case RESET_RECEIVE_CFS:
                notificationService.resetReceiveCfs(id,info.date,info.files,session);
                break;
            case RECEIVE_INCI:
                notificationService.receiveInci(id,info.date,info.files,session);
                break;
            case RESET_RECEIVE_INCI:
                notificationService.resetReceiveInci(id,info.date,info.files,session);
                break;
            case RECEIVE_DAV:
                notificationService.receiveDav(id,info.date,info.davExpiringDate,info.davOfficialRequestingDate,info.davNotificationNumber,info.files,session);
                break;
            default:
                break;
        }
        return Response.ok().build();
    }

    @GET
    @Path("/xls")
    @ApiOperation("Get xls files exported of notifications")

    public Response doExportToXls(@Auth JubiqSession session,
                                  @DefaultValue("") @QueryParam("query") String query,
                                  @DefaultValue(DEFAULT_ORDER) @QueryParam("order") String order,
                                  @DefaultValue("") @QueryParam("filter") String filter,
                                  @QueryParam("fields") String fields,
                                  @QueryParam("templateId") Long templateId) throws IOException {

        if(filter.length() > 0) {
            if(query == null || query.length() == 0) {
                query = filterService.get(filter).query;
            }
            else{
                query += " AND " + filterService.get(filter).query;
            }
        }
        List<Notification> notifications = this.notificationService.searchForExportXls(query,10000,0,order);
        List<String> fieldNames;
        String sheetName;
        if(templateId != null) {
            XlsTemplate template = xlsTemplateService.get(templateId);
            fieldNames = template.fieldNames;
            sheetName = template.name;
        } else {
            fieldNames = Arrays.asList(fields.split(","));
            sheetName = "Requests";
        }
        File file = this.notificationService.exportXls(sheetName, notifications,fieldNames,session.userId);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=notifications.xls");
        return builder.build();
    }

    @POST
    @Path("/{id}/duplicate")
    @ApiOperation("Duplicate a notification")
    public Response duplicate(@Auth JubiqSession session, @PathParam("id") Long id) {

        Notification duplicated = notificationService.duplicate(id,session);
        return Response.ok(duplicated).build();
    }

    @Override
    public Response doDelete(@Auth JubiqSession session, Long aLong) throws UnauthorizedException, JubiqPersistenceException {
        Notification notification = notificationService.getDao().get(aLong);
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("Must be follower to delete a notification");
        }
        return super.doDelete(session, aLong);
    }

    @GET
    @Path("/{id}/dav-form")
    @ApiOperation("Get dav form by notification id")
    public Response doExportToDavForm(@PathParam("id") Long id) throws IOException {

        File file = this.notificationService.exportToDavForm(id,null);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=" + id + ".xls");
        return builder.build();
    }

    @GET
    @Path("/{id}/ingredients")
    @ApiOperation("Get ingredients of a notification")
    public Response getIngredients(@Auth JubiqSession session, @PathParam("id") Long notificationId) {
        Map<String,List<Ingredient>> map = notificationService.getNotificationIngredients(notificationId);
        return Response.ok(map).build();
    }

    @GET
    @Path("/execute-notification-expire-job")
    @ApiOperation("Execute notification expire job")
    public Response executeNotificationExpireJob(@Auth JubiqSession session) {
        notificationService.executeNotificationExpireJob();
        return Response.ok().build();
    }

    @GET
    @Path("/execute-cfs-inci-expire-job")
    @ApiOperation("Execute cfs inci expire job")
    public Response excuteCfsInciExpireJob(@Auth JubiqSession session) {
        notificationService.excuteCfsInciExpireJob();
        return Response.ok().build();
    }
}
