package com.jubiq.loreal.umgr.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.umgr.models.Permission;

/**
 * Created by vietnq2 on 11/30/15.
 */
@Singleton
public class PermissionService extends JubiqService<String,Permission> {
    @Inject
    public PermissionService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
        this.dao = daoFactory.createDao(Permission.class,String.class);
    }
}
