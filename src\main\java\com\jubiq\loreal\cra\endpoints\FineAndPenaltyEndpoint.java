package com.jubiq.loreal.cra.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.cra.models.CraFineAndPenalty;
import com.jubiq.loreal.cra.services.FineAndPenaltyService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by HH on 7/7/25.
 * REST endpoint for Fine and Penalty management
 */
@Path("/api/fine-and-penalties")
@Api("Fine and Penalties")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Singleton
public class FineAndPenaltyEndpoint extends JubiqEndpoint<Long, CraFineAndPenalty> {
    
    @Inject
    public FineAndPenaltyEndpoint(FineAndPenaltyService fineAndPenaltyService) {
        this.service = fineAndPenaltyService;
    }
}
