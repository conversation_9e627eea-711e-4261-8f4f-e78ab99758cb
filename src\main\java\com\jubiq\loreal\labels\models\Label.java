package com.jubiq.loreal.labels.models;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.models.PresentationDetail;
import com.jubiq.loreal.notifications.util.Utils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Created by THANHLX on 5/17/2018.
 */
public class Label extends JubiqEntity<Long> {
    public LabelStatus status;
    public String creatorEmail;
    public String creatorFullName;
    public List<Long> notificationIds;
    public Long assigneeId;
    public String assigneeEmail;
    public String assigneeFullName;
    public Long groupId;
    public Long followerId;
    public String followerEmail;
    public String followerFullName;
    public String productName;
    public String userManual;
    public String importer;
    public String distributor;
    public String alarm;
    public String ingredients;
    public String baseIngredients;
    public String colorIngredients;
    public String lotNumber;
    public String manufacturingDate;
    public String expirationDate;
    public String hotline;
    public String davNotificationNumber;
    public String manufacturer;
    public String preservation;
    public String netVolume;
    public String uses;
    public String barCode;
    public String sapCode;
    public String other;
    public Boolean sciValidated;
    public Boolean logisticValidated;
    public Date submittedDate;
    public Date sciValidatedDate;
    public Date logisticValidatedDate;
    public String brandName;
    public String bundle;
    public String owner;
    @Serialized
    public Map<String,String> files;
    public Label() {
        files = new HashMap<String, String>();
    }

    @NotAColumn
    public String additionIngredient;

    // chua danh sach cac presentation goc cua notification ma label thuoc ve
    @NotAColumn
    public List<PresentationDetail> notificationPresentationDetails;

    // chua cac presentation ma label mapping
    public List<String> presentationDetailIds;

    @NotAColumn
    public List<PresentationDetail> presentationDetails;

    public Set<String> compareFields(Label other) {
        Set<String> differentFields = new HashSet<>();
        Class<?> clazz = getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value1 = field.get(this);
                Object value2 = field.get(other);
                if(value1 == null && value2 == null) continue;
                if(field.getName().equals("mapDuplicateNotification")
                        || field.getName().equals("duplicateNotifications")
                        || field.getName().equals("advertising")
                        || field.getName().equals("notificationPresentationDetails")
                        || field.getName().equals("logisticValidatedDate")
                        || field.getName().equals("sciValidatedDate")
                        || field.getName().equals("submittedDate")
                        || field.getName().equals("sciValidated")
                        || field.getName().equals("followerFullName")
                        || field.getName().equals("groupId")
                        || field.getName().equals("assigneeFullName")
                        || field.getName().equals("followerId")
                        || field.getName().equals("assigneeId")
                        || field.getName().equals("creatorFullName")
                        || field.getName().equals("status")
                        || field.getName().equals("sapCode")
                        || field.getName().equals("additionIngredient")

                ) continue;
                if(field.getName().equals("notificationIds")){
                    List<Long> stringSplit1 = (List<Long>)value1;
                    List<Long> stringSplit2 = (List<Long>)value2;
                    if(!(stringSplit1.containsAll(stringSplit2) && !stringSplit2.containsAll(stringSplit1))) differentFields.add("Requests");
                    continue;
                }
                if(field.getName().equals("presentationDetailIds")){
                    List<String> lit1 = (List<String>)value1;
                    List<String> lit2 = (List<String>)value2;
                    if(!(lit1.containsAll(lit2) && lit2.containsAll(lit1))) differentFields.add("SKUs");
                    continue;
                }
                if(value1 instanceof Map && value2 instanceof Map && (((Map)value1).equals((Map)value2))){
                    continue;
                }
                if(value1 instanceof Date && value2 instanceof Date && Utils.convertDate((Date)value1).equals(Utils.convertDate((Date)value2))){
                    continue;
                }
                if (value1 != null && value2 == null || value1 == null && value2 != null || !value1.equals(value2)) {
                    differentFields.add(Utils.lowerCaseToCamelCase(getFieldName(field.getName())));
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return differentFields;
    }
    public String getFieldName(String field){
        String fieldReturn = field;
        switch (field){
            case "productName":
                fieldReturn = "Tên sản phẩm";
                break;
            case "brandName":
                fieldReturn = "Nhãn hàng";
                break;
            case "userManual":
                fieldReturn = "Hướng dẫn sử dụng";
                break;
            case "distributor":
                fieldReturn = "Phân phối bởi";
                break;
            case "lotNumber":
                fieldReturn = "Số lô";
                break;
            case "expirationDate":
                fieldReturn = "Ngày hết hạn";
                break;
            case "colorIngredients":
                fieldReturn = "Thành phần của màu trang điểm";
                break;
            case "baseIngredients":
                fieldReturn = "Thành phần dòng sản phẩm";
                break;
            case "ingredients":
                fieldReturn = "Thành phần";
                break;
            case "manufacturer":
                fieldReturn = "Nước sản xuất";
                break;
            case "other":
                fieldReturn = "Thông tin khác";
                break;
            case "importer":
                fieldReturn = "Chịu trách nhiệm đưa sản phẩm ra thị trường";
                break;
            case "alarm":
                fieldReturn = "Cảnh báo";
                break;
            case "manufacturingDate":
                fieldReturn = "Ngày sản xuất";
                break;
            case "hotline":
                fieldReturn = "Số điên thoại tư vấn khách hàng";
                break;
            case "davNotificationNumber":
                fieldReturn = "Số công bố";
                break;
            case "preservation":
                fieldReturn = "Bảo quản";
                break;
            case "uses":
                fieldReturn = "Công dụng, hiệu quả";
                break;
        }
        return fieldReturn;
    }
}
