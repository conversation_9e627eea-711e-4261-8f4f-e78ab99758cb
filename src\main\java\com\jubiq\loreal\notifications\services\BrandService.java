package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Brand;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/5/16.
 */
@Singleton
public class BrandService extends JubiqService<Long,Brand> {

    private RangeService rangeService;
    @Inject
    public BrandService(DaoFactory daoFactory, RangeService rangeService) {
        this.dao = daoFactory.createDao(Brand.class,Long.class);
        this.rangeService = rangeService;
    }

    @Override
    public Brand get(Long aLong) throws JubiqPersistenceException {
        Brand brand = super.get(aLong);
        brand.ranges = rangeService.rangesOfBrand(brand.id);
        return brand;
    }

    @Override
    public List<Brand> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        List<Brand> brands = super.search(query, limit, offset, order);
        for(Brand brand : brands) {
            brand.ranges = rangeService.rangesOfBrand(brand.id);
        }
        return brands;
    }

    @Override
    public void afterUpdate(Long id,Brand entity) throws JubiqPersistenceException {
        String sql = "update notifications set brand_name=:brandName,updated=:updated where brand_id=:brandId and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("brandName",entity.name);
        map.put("brandId",entity.id);
        this.dao.executeUpdate(sql,map);
    }

    public Brand findByName(String brandName) {
        String sql = "select * from brands where name=:name and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("name",brandName);
        List<Brand> brands = this.dao.search(sql,map);
        if(brands != null && brands.size() > 0) {
            return brands.get(0);
        } else {
            throw new EntityNotFoundException("No brand with name:" + brandName);
        }
    }
}
