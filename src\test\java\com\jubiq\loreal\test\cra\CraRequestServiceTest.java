package com.jubiq.loreal.test.cra;

import com.jubiq.loreal.advertisings.models.KeyvisualMessage;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.models.CRAStatus;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.cra.services.CraRequestService;
import org.junit.Test;
import org.junit.Before;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Test class for CraRequestService email notification functionality
 */
public class CraRequestServiceTest {

    @Mock
    private CraRequestService craRequestService;

    private JubiqSession followerSession;
    private JubiqSession sciSession;
    private CraRequest testCraRequest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // Setup follower session (MKT user)
        followerSession = new JubiqSession();
        followerSession.userId = 1001L;
        followerSession.fullName = "<PERSON> (MKT)";
        followerSession.userEmail = "<EMAIL>";
        followerSession.groupName = "Marketing";
        followerSession.groupId = 3000L;
        
        // Setup SCI session
        sciSession = new JubiqSession();
        sciSession.userId = 2001L;
        sciSession.fullName = "Jane Smith (SCI)";
        sciSession.userEmail = "<EMAIL>";
        sciSession.groupName = "SCI Manager";
        sciSession.groupId = 2001L;
        
        // Setup test CRA request
        testCraRequest = new CraRequest();
        testCraRequest.id = 123L;
        testCraRequest.followerId = 1001L;
        testCraRequest.followerEmail = "<EMAIL>";
        testCraRequest.followerFullName = "John Doe (MKT)";
        testCraRequest.assigneeId = 2001L;
        testCraRequest.assigneeEmail = "<EMAIL>";
        testCraRequest.assigneeFullName = "Jane Smith (SCI)";
        testCraRequest.expositionDetail = "Test CRA Request for Email Notifications";
        testCraRequest.status = CRAStatus.WAIT_FOR_STRD_RISK_ASSESSMENT;
        testCraRequest.messages = new ArrayList<>();
    }

    @Test
    public void testNewMessageFromFollowerToAssignee() {
        // Create a new message from follower (MKT)
        KeyvisualMessage newMessage = new KeyvisualMessage();
        newMessage.userId = followerSession.userId.intValue();
        newMessage.email = followerSession.userEmail;
        newMessage.fullName = followerSession.fullName;
        newMessage.message = "Hello, I have a question about this CRA request.";
        newMessage.createdTime = null; // This indicates it's a new message
        
        // Add the new message to the request
        testCraRequest.messages.add(newMessage);
        
        // Create updated request for testing
        CraRequest updatedRequest = new CraRequest();
        updatedRequest.id = testCraRequest.id;
        updatedRequest.followerId = testCraRequest.followerId;
        updatedRequest.followerEmail = testCraRequest.followerEmail;
        updatedRequest.followerFullName = testCraRequest.followerFullName;
        updatedRequest.assigneeId = testCraRequest.assigneeId;
        updatedRequest.assigneeEmail = testCraRequest.assigneeEmail;
        updatedRequest.assigneeFullName = testCraRequest.assigneeFullName;
        updatedRequest.expositionDetail = testCraRequest.expositionDetail;
        updatedRequest.status = testCraRequest.status;
        updatedRequest.messages = Arrays.asList(newMessage);
        
        // This test demonstrates the expected behavior:
        // When a follower (MKT user) sends a message, it should be sent to the assignee
        System.out.println("Test Case: Follower sends message to assignee");
        System.out.println("Expected: Email sent to " + testCraRequest.assigneeEmail);
        System.out.println("Subject: New message from MKT: " + testCraRequest.followerFullName);
        System.out.println("Message: " + newMessage.message);
    }

    @Test
    public void testNewMessageFromSciToFollower() {
        // Create a new message from SCI user
        KeyvisualMessage newMessage = new KeyvisualMessage();
        newMessage.userId = sciSession.userId.intValue();
        newMessage.email = sciSession.userEmail;
        newMessage.fullName = sciSession.fullName;
        newMessage.message = "Thank you for your question. Here is the response...";
        newMessage.createdTime = null; // This indicates it's a new message
        
        // Add the new message to the request
        testCraRequest.messages.add(newMessage);
        
        // This test demonstrates the expected behavior:
        // When a SCI user sends a message, it should be sent to the follower
        System.out.println("Test Case: SCI user sends message to follower");
        System.out.println("Expected: Email sent to " + testCraRequest.followerEmail);
        System.out.println("Subject: New message from " + sciSession.fullName);
        System.out.println("Message: " + newMessage.message);
    }

    @Test
    public void testNoEmailForExistingMessage() {
        // Create a message that already has createdTime (existing message)
        KeyvisualMessage existingMessage = new KeyvisualMessage();
        existingMessage.userId = followerSession.userId.intValue();
        existingMessage.email = followerSession.userEmail;
        existingMessage.fullName = followerSession.fullName;
        existingMessage.message = "This is an existing message.";
        existingMessage.createdTime = "2025-01-19 10:30:00"; // Already has timestamp
        
        // Add the existing message to the request
        testCraRequest.messages.add(existingMessage);
        
        // This test demonstrates the expected behavior:
        // When a message already has createdTime, no email should be sent
        System.out.println("Test Case: Existing message (no email should be sent)");
        System.out.println("Expected: No email sent because message already has createdTime");
        System.out.println("Message createdTime: " + existingMessage.createdTime);
    }

    @Test
    public void testEmailValidation() {
        // Test case where email addresses might be null or empty
        CraRequest requestWithNullEmails = new CraRequest();
        requestWithNullEmails.id = 456L;
        requestWithNullEmails.followerId = 1001L;
        requestWithNullEmails.followerEmail = null; // Null email
        requestWithNullEmails.assigneeEmail = ""; // Empty email
        
        KeyvisualMessage newMessage = new KeyvisualMessage();
        newMessage.message = "Test message with invalid emails";
        newMessage.createdTime = null;
        
        requestWithNullEmails.messages = Arrays.asList(newMessage);
        
        System.out.println("Test Case: Invalid email addresses");
        System.out.println("Expected: No email sent due to null/empty recipient emails");
        System.out.println("Follower email: " + requestWithNullEmails.followerEmail);
        System.out.println("Assignee email: " + requestWithNullEmails.assigneeEmail);
    }
}
