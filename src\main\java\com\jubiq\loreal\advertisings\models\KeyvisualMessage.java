package com.jubiq.loreal.advertisings.models;

import com.jubiq.loreal.common.util.Serialized;

import java.util.Map;
import java.util.Objects;

public class KeyvisualMessage {
    public int userId;
    public String email;
    public String fullName;
    public String message;
    @Serialized
    public Map<String,String> attachmentFiles;
    public String createdTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        KeyvisualMessage that = (KeyvisualMessage) o;
        return Objects.equals(email, that.email) && Objects.equals(fullName, that.fullName) && Objects.equals(message, that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email, fullName, message);
    }
}
