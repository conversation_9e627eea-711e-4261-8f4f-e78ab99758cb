package com.jubiq.loreal.common.endpoints.errorhandlers;

import com.jubiq.loreal.common.exceptions.ErrorMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;

/**
 * Created by vietnq2 on 12/21/15.
 */
public abstract class JubiqExceptionMapper<E extends Exception> implements ExceptionMapper<E> {

    private static Logger LOGGER = LoggerFactory.getLogger(JubiqExceptionMapper.class);

    protected abstract ErrorMessage getErrorMessage(E e);
    protected abstract Response.Status getStatus(E e);

    @Override
    public Response toResponse(E e) {
        LOGGER.error("Exception occurs: ", e);
        return Response.status(getStatus(e)).entity(getErrorMessage(e)).build();
    }
}
