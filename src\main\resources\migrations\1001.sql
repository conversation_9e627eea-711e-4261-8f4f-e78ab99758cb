--liquibase formatted sql
--changeset vietnq:1001
delete from groups_permissions where group_id=1000;
delete from permissions;

INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:CREATE:USER','Allow to create user',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:CREATE:GROUP','Allow to create group',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:ASSIGN','Allow to set assignee',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:CREATE','Allow to create notification request',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:REJECT','Allow to reject notification request',1447653450436,NULL,NULL);


INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (1,1000,'*:*',1447653450436,NULL,NULL);

--rollback;