package com.jubiq.loreal.security;

import com.google.common.base.Optional;
import com.google.inject.Inject;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.umgr.services.SessionService;
import io.dropwizard.auth.AuthenticationException;
import io.dropwizard.auth.Authenticator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by vietnq on 11/21/15.
 */
public class JubiqAuthenticator implements Authenticator<String,JubiqSession> {

    private static Logger LOGGER = LoggerFactory.getLogger(JubiqAuthenticator.class);

    private SessionService sessionService;

    @Inject
    public JubiqAuthenticator(SessionService sessionService) {
        this.sessionService = sessionService;
    }

    @Override
    public Optional<JubiqSession> authenticate(String s) throws AuthenticationException {
        LOGGER.debug("authenticating access token : {}",s);
        try {
            JubiqSession session = sessionService.get(s);
            if(session.isExpired()) {
                LOGGER.debug("Session for access token {} is expired",s);
                return Optional.absent();
            }
            LOGGER.debug("Authentication OK");
            return Optional.of(session);
        } catch (EntityNotFoundException e) {
            LOGGER.debug("No session found for access token: " + s);
            return Optional.absent();
        } catch(JubiqPersistenceException e) {
            throw new AuthenticationException(new JubiqPersistenceException("Persistence exception when validating access token"));
        }
    }
}
