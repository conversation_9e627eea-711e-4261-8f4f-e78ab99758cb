--liquibase formatted sql
--changeset thanhlx:045
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('validate-label.alert.email.template', 'Label of notification ${notificationId} (Product Name: ${productName}) is validated by ${sourceFullName}.
<br>
Please click <a href="http://***********/label/update/${labelId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('submit-label.alert.email.template', 'Label of notification ${notificationId} (Product Name: ${productName}) is submitted by ${sourceFullName}. <br> Please click <a href="http://***********/label/process/${labelId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('reject-label.alert.email.template', 'Label of notification ${notificationId} (Product Name: ${productName}) is rejected by ${sourceFullName}. <br> Please click <a href="http://***********/label/update/${labelId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('delete-label.alert.email.template', 'Label of notification ${notificationId} (Product Name: ${productName}) is deleted by ${sourceFullName}.', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('delete.alert.email.template', 'Notification ${notificationId} (Product Name: ${productName}) is deleted by ${sourceFullName}.', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('create.user.email.template', 'Your password:%s<br>Please click <a href="http://***********/site/login">here</a> to login.', NULL, NULL, NULL, NULL, NULL);
--rollback;