package com.jubiq.loreal.notifications.models.enumerations;

/**
 * Created by vietnq2 on 1/19/16.
 */
public enum ExportedField {
    id("Request No"),
    presentationType("Presentation Type"),
    followerFullName("Follower fullname"),
    followerEmail("Email of follower"),
    creatorFullN<PERSON>("Creator fullname"),
    creatorEmail("Email of creator"),
    status("Status"),
    brand("Brand"),
    productRange("Product Range"),
    productName("English Product Name"),
    individualProductName("Individual Product Name"),
    productType("Product Type"),
    aw("AW Files"),
    launchForm("Launch Form Files"),
    launchTime("Launch Time"),
    shipmentRequestTime("Shipment Request Time"),
    assigneeFullName("Assignee Fullname"),
    assigneeEmail("Email of assignee"),
    validatedTime("Validated Time"),
    cfsRequestingDate("Date of Requesting CFS"),
    cfsAvailable("CFS Available"),
    cfsReceivingDate("Date of Receiving CFS"),
    cfs("CFS Files"),
    inciRequestingDate("Date of Requesting INCI"),
    inciAvailable("INCI Available"),
    inciReceivingDate("Date of Receiving INCI"),
    inci("INCI Files"),
    davNotificationNumber("Notification number"),
    davReceivingDate("Receiving date"),
    davExpiringDate("Expiry date"),
    dav("DAV Files"),
    productCode("Product Code"),
    barCode("Bar Code"),
    formulaNumber("Formula Number"),
    filCode("Fil Code"),
    ingredients("Ingredients"),
    compositeNumber("Composite Number"),
    netWeight("Net Weight"),
    volume("Volume"),
    commonShadeName("Shade Name"),
    shadeName("Individual Shade Name"),
    shadeCode("Shade Code"),
    intendedUse("Intended Use"),
    individualIntendedUse("Individual Intended Use"),
    manufacturerCountry("Manufacturer Country"),
    manufacturer("Manufacturer"),
    manufacturerAddress("Address of Manufacturer"),
    assemblerCountry("Assembler Country"),
    assemblerAddress("Address of Assembler"),
    assembler("Assembler"),
    assemblerType("Assembler Type"),
    requestDate("Request Date"),
    otherPresentationType("Specify other presentation"),
    productNameVn("Vietnamese Product Name"),
    listingNameVn("Listing Vietnamese name"),
    submittingDate("Date of Submitting"),
    revisingDate("Date of Revising"),
    davRequestingDate("Date of Submitting to DAV"),
    davAvailable("DAV available"),
    discontinued("Discontinued"),
    intentDiscontinuedDate("First intent of Discontinue Date"),
    officialDiscontinuedDate("Official Discontinue Date"),
    exporterCountry("Exporter Country"),
    exporter("Exporter"),
    exporterAddress("Address of Exporter"),
    hasAdvertising("Advertising"),
    hasLabel("Label"),
    notiType("Notification Type");

    public String displayName;

    ExportedField(String displayName) {
        this.displayName = displayName;
    }
}
