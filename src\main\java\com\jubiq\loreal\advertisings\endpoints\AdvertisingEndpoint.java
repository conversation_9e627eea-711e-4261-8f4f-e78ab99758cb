package com.jubiq.loreal.advertisings.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.advertisings.models.Advertising;
import com.jubiq.loreal.advertisings.models.AdvertisingXlsTemplate;
import com.jubiq.loreal.advertisings.services.AdvertisingService;
import com.jubiq.loreal.advertisings.services.AdvertisingXlsTemplateService;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.umgr.services.UserService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 11/29/15.
 */
@Path("/api/advertisings")
@Api("advertisings")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class AdvertisingEndpoint extends JubiqEndpoint<Long,Advertising> {
    private static Logger LOGGER = LoggerFactory.getLogger(AdvertisingEndpoint.class);

    protected AdvertisingService advertisingService;
    protected AdvertisingXlsTemplateService advertisingXlsTemplateService;
    private UserService userService;

    @Inject
    public AdvertisingEndpoint(AdvertisingService service, UserService userService, AdvertisingXlsTemplateService advertisingXlsTemplateService) {
        this.service = this.advertisingService = service;
        this.userService = userService;
        this.advertisingXlsTemplateService = advertisingXlsTemplateService;
    }

    @POST
    @Path("/{id}/submit")
    @ApiOperation("submit an advertising")
    public Response doSubmit(@Auth JubiqSession session, @PathParam("id") Long id) {
        advertisingService.submitToSci(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/revise")
    @ApiOperation("revise an advertising")
    public Response revise(@Auth JubiqSession session, @PathParam("id") Long id) {
        advertisingService.revise(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/reject")
    @ApiOperation("Reject an advertising")
    public Response reject(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        advertisingService.reject(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/validate")
    @ApiOperation("Validate an advertising")
    public Response validate(@Auth JubiqSession session, @PathParam("id") Long id) {
        advertisingService.validate(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/submit-license")
    @ApiOperation("submit an license")
    public Response doSubmitLicense(@Auth JubiqSession session, @PathParam("id") Long id, LicenseInfo info) {
        advertisingService.submitLicense(id, info.licenseRequestingDate, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/receive-license")
    @ApiOperation("receive an license")
    public Response doReceiveLicense(@Auth JubiqSession session, @PathParam("id") Long id, LicenseInfo info) {
        advertisingService.receiveLicense(id, info.licenseReceivingDate, info.licenseExpiredDate, info.licenseNumber, info.licenseFiles, info.otherFiles, info.proofDocuments, session);
        return Response.ok().build();
    }

    @GET
    @Path("/xls")
    @ApiOperation("Get xls files exported of advertising")

    public Response doExportToXls(@Auth JubiqSession session,
                                  @DefaultValue("") @QueryParam("query") String query,
                                  @DefaultValue(DEFAULT_ORDER) @QueryParam("order") String order,
                                  @DefaultValue("") @QueryParam("filter") String filter,
                                  @QueryParam("fields") String fields,
                                  @QueryParam("templateId") Long templateId) throws IOException {

        if (filter.length() > 0) {
            if (query == null || query.length() == 0) {
                query = filterService.get(filter).query;
            } else {
                query += " AND " + filterService.get(filter).query;
            }
        }
        List<Advertising> notifications = this.advertisingService.search(query, 10000, 0, order);
        List<String> fieldNames;
        String sheetName;
        if (templateId != null) {
            AdvertisingXlsTemplate template = advertisingXlsTemplateService.get(templateId);
            fieldNames = template.fieldNames;
            sheetName = template.name;
        } else {
            fieldNames = Arrays.asList(fields.split(","));
            sheetName = "Advertisings";
        }
        File file = this.advertisingService.exportXls(sheetName, notifications, fieldNames, session.userId);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=advertisings.xls");
        return builder.build();
    }

    @GET
    @Path("/query-notification")
    @ApiOperation("Query advertising by notification id")
    public Response queryNotification(@Auth JubiqSession session, @QueryParam("notificationId") Long notificationId) {
        List<Advertising> labels = advertisingService.findByNotificationId(notificationId);
        return Response.ok(labels).build();
    }

    @POST
    @Path("/{id}/recall")
    @ApiOperation("Recall a advertising")
    public Response doRecall(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) {
        advertisingService.recall(id, reason, session);
        return Response.ok().build();
    }
}
