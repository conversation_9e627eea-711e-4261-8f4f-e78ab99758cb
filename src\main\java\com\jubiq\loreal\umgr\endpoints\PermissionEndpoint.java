package com.jubiq.loreal.umgr.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.umgr.models.Permission;
import com.jubiq.loreal.umgr.services.PermissionService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq2 on 11/30/15.
 */
@Path("/api/permissions")
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Api("permissions")
@Singleton
public class PermissionEndpoint extends JubiqEndpoint<String,Permission> {
    @Inject
    public PermissionEndpoint(PermissionService permissionService) {
        this.service = permissionService;
    }
}
