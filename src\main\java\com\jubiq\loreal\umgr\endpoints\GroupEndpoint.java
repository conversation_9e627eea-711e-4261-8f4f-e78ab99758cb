package com.jubiq.loreal.umgr.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.GroupNode;
import com.jubiq.loreal.umgr.services.GroupService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import static javax.ws.rs.core.MediaType.*;

/**
 * Created by vietnq2 on 11/30/15.
 */
@Path("/api/groups")
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Produces(APPLICATION_JSON)
@Api("groups")
@Singleton
public class GroupEndpoint extends JubiqEndpoint<Long,Group> {

    private GroupService groupService;
    @Inject
    public GroupEndpoint(GroupService groupService) {
        this.service = this.groupService = groupService;
    }

    @GET
    @Path("/tree")
    public Response groupTree(@Auth JubiqSession session,
                              @DefaultValue("created desc") @QueryParam("order") String orderBy) throws UnauthorizedException, JubiqPersistenceException {
        authorize(session, find_permission);
        GroupNode node = groupService.groupTree(orderBy);
        return Response.ok(node).build();
    }
}
