package com.jubiq.loreal.advertisings.models;

public enum AdvertisingExportedField {
    id("ADV request No"),
    status("Status"),
    advertisingMediaSelection("Advertising Media Selection"),
    requestIds("Requests"),
    assigneeFullName("Fullname of assignee"),
    assigneeEmail("Email of assignee"),
    followerFullName("Fullname of follower"),
    followerEmail("Email of follower"),
    creatorFullN<PERSON>("Fullname of creator"),
    creatorEmail("Email of creator"),
    description("Description"),
    submittedDate("Date of submitting to SCI"),
    validatedTime("Validated Time"),
    licenseRequestingDate("Date of submitting license"),
    licenseNumber("License number"),
    licenseReceivingDate("Date of receiving license"),
    licenseExpiredDate("Date of expried license"),
    brand("Brand"),
    revisingDate("Date of revising"),
    contentFiles("Content Files"),
    referencesFiles("References Files");

    public String displayName;

    AdvertisingExportedField(String displayName) {
        this.displayName = displayName;
    }
}
