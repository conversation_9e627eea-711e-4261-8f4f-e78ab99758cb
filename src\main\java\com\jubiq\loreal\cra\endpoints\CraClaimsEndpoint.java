package com.jubiq.loreal.cra.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.models.CraClaims;
import com.jubiq.loreal.cra.services.CraClaimsService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.Date;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by AI Assistant on 7/11/25.
 * REST endpoint for CRA Claims management
 */
@Path("/api/cra-claims")
@Api("CRA Claims")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Singleton
public class CraClaimsEndpoint extends JubiqEndpoint<Long, CraClaims> {
    
    private CraClaimsService craClaimsService;
    
    @Inject
    public CraClaimsEndpoint(CraClaimsService craClaimsService) {
        this.service = this.craClaimsService = craClaimsService;
    }
    
    @GET
    @Path("/by-request/{craId}")
    @ApiOperation("Get claims by CRA ID")
    public Response getClaimsByCraId(@Auth JubiqSession session,
                                    @PathParam("craId") Long craId) throws JubiqPersistenceException {

        List<CraClaims> claims = craClaimsService.getClaimsByRequestId(craId);
        return Response.ok(claims).build();
    }
    
    @GET
    @Path("/by-approval-status")
    @ApiOperation("Get claims by approval status")
    public Response getClaimsByApprovalStatus(@Auth JubiqSession session,
                                             @QueryParam("status") String approvalStatus,
                                             @DefaultValue("1") @QueryParam("page") int page,
                                             @DefaultValue("20") @QueryParam("size") int size) throws JubiqPersistenceException {
        
        if (approvalStatus == null || approvalStatus.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("Approval status is required").build();
        }
        
        int offset = (page - 1) * size;
        List<CraClaims> claims = craClaimsService.getClaimsByApprovalStatus(approvalStatus, size, offset);
        return Response.ok(claims).build();
    }
    
    @GET
    @Path("/by-mkt-status")
    @ApiOperation("Get claims by marketing acceptance status")
    public Response getClaimsByMktStatus(@Auth JubiqSession session,
                                        @QueryParam("status") String mktStatus,
                                        @DefaultValue("1") @QueryParam("page") int page,
                                        @DefaultValue("20") @QueryParam("size") int size) throws JubiqPersistenceException {
        
        if (mktStatus == null || mktStatus.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("Marketing status is required").build();
        }
        
        int offset = (page - 1) * size;
        List<CraClaims> claims = craClaimsService.getClaimsByMktStatus(mktStatus, size, offset);
        return Response.ok(claims).build();
    }
    
    @PUT
    @Path("/{id}/approve")
    @ApiOperation("Approve or reject a claim")
    public Response approveClaim(@Auth JubiqSession session,
                                @PathParam("id") Long id,
                                @ApiParam @FormParam("approvalStatus") String approvalStatus,
                                @ApiParam @FormParam("approverType") String approverType) throws JubiqPersistenceException, UnauthorizedException {
        
        CraClaims claims = craClaimsService.get(id);
        if (claims == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        
        claims.approvalStatus = approvalStatus;
        claims.approvalDate = new Date();
        claims.approverId = session.userId;
        claims.approverType = approverType;
        
        craClaimsService.update(id, claims, session);
        return Response.ok().build();
    }
    
    @PUT
    @Path("/{id}/mkt-accept")
    @ApiOperation("Marketing acceptance of a claim")
    public Response mktAcceptClaim(@Auth JubiqSession session,
                                  @PathParam("id") Long id,
                                  @ApiParam @FormParam("mktAcceptedStatus") String mktAcceptedStatus) throws JubiqPersistenceException, UnauthorizedException {

        CraClaims claims = craClaimsService.get(id);
        if (claims == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        claims.mktAcceptedStatus = mktAcceptedStatus;
        claims.mktAcceptedDate = new Date();
        claims.mktAcceptedBy = session.userId;
        
        craClaimsService.update(id, claims, session);
        return Response.ok().build();
    }
}
