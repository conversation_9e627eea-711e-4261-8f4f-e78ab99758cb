package com.jubiq.loreal.umgr.models;

import com.jubiq.loreal.common.model.JubiqEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vietnq on 12/14/15.
 */
public class UserPreference extends JubiqEntity<Long> {
    public List<String> filters;
    public List<String> labelFilters;
    public List<String> advertisingFilters;
    public Long userId;
    public List<String> selectedFields;
    public List<String> selectedLabelFields;
    public List<String> selectedAdvertisingFields;
    public List<String> selectedCraFields;

    public UserPreference() {
        this.filters = new ArrayList<String>();
        this.selectedFields = new ArrayList<String>();
        this.selectedLabelFields = new ArrayList<String>();
        this.selectedCraFields = new ArrayList<String>();
        this.selectedAdvertisingFields = new ArrayList<String>();
        this.labelFilters = new ArrayList<String>();
        this.advertisingFilters = new ArrayList<String>();
    }

    public UserPreference(Long userId) {
        this.userId = userId;
        this.filters = new ArrayList<String>();
        this.selectedFields = new ArrayList<String>();
        this.selectedLabelFields = new ArrayList<String>();
        this.selectedAdvertisingFields = new ArrayList<String>();
        this.selectedCraFields = new ArrayList<String>();
        this.labelFilters = new ArrayList<String>();
        this.advertisingFilters = new ArrayList<String>();
    }
}

