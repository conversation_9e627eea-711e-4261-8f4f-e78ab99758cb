--liquibase formatted sql
--changeset vietnq:1002
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:FIND','Allow to search for notification request',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:READ','Allow to view notification request',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:DELETE','Allow to delete notification request',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:NOTIFICATION:UPDATE','Allow to reject notification request',1447653450436,NULL,NULL);

INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (2,3000,'API:NOTIFICATION:CREATE',1447653450436,NULL,NULL);
INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (3,3000,'API:NOTIFICATION:UPDATE',1447653450436,NULL,NULL);
INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (4,3000,'API:NOTIFICATION:FIND',1447653450436,NULL,NULL);
INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (5,3000,'API:NOTIFICATION:DELETE',1447653450436,NULL,NULL);
INSERT INTO groups_permissions(id,group_id,permission_id,created,updated,deleted) VALUES (6,3000,'API:NOTIFICATION:READ',1447653450436,NULL,NULL);

--rollback;