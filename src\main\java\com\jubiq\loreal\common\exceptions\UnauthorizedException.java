package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 11/25/15.
 */
public class UnauthorizedException extends JubiqException {

    public UnauthorizedException(Throwable e) {
        super(e);
    }

    public UnauthorizedException(String message) {
        super(message);
    }

    public UnauthorizedException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.UNAUTHORIZED.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.UNAUTHORIZED.name();
    }
}
