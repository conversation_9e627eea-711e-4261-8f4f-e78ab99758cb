package com.jubiq.loreal.cra.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.cra.models.CraFineAndPenalty;

/**
 * Created by HH on 7/7/25.
 * Service for Fine and Penalty management
 */
@Singleton
public class FineAndPenaltyService extends JubiqService<Long, CraFineAndPenalty> {
    
    @Inject
    public FineAndPenaltyService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(CraFineAndPenalty.class, Long.class);
    }
}
