package com.jubiq.loreal.test.notification;

import com.google.inject.Injector;
import com.jubiq.loreal.LorealApplication;
import com.jubiq.loreal.LorealConfiguration;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.test.JubiqAbstractTest;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;
import io.dropwizard.testing.ResourceHelpers;
import io.dropwizard.testing.junit.DropwizardAppRule;
import liquibase.Liquibase;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.LiquibaseException;
import liquibase.resource.ClassLoaderResourceAccessor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.util.List;

/**
 * Created by vietnq on 11/29/15.
 */
public class NotificationServiceTest extends JubiqAbstractTest {
    private Injector injector;
//    @Rule
    public final DropwizardAppRule<LorealConfiguration> RULE = new DropwizardAppRule<LorealConfiguration>(LorealApplication.class,
            ResourceHelpers.resourceFilePath("loreal.yml"));

//    @Override
    public void initData() throws LiquibaseException {
        this.daoFactory = new DaoFactory(dbi);
        liquibase = new Liquibase("test_service.xml", new ClassLoaderResourceAccessor(), new JdbcConnection(handle.getConnection()));
        liquibase.update("test");
    }

//    @Before
    public void setUp() {
        LorealApplication application = RULE.getApplication();
        injector = application.guiceBundle.getInjector();
    }

//    @Test
//    public void testGuice() {
//        UserService userService = injector.getInstance(UserService.class);
//        List<User> users = userService.getAll();
//        Assert.assertEquals(1,users.size());
//    }
}
