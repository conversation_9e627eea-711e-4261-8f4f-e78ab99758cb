package com.jubiq.loreal.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Names;
import com.jubiq.loreal.FileConfiguration;
import com.jubiq.loreal.LorealConfiguration;
import com.jubiq.loreal.common.service.AsyncService;
import com.jubiq.loreal.common.service.AsyncServiceImpl;
import com.jubiq.loreal.common.service.MailConfiguration;
import com.jubiq.loreal.quartz.SchedulerJobFactory;
import io.dropwizard.jdbi.DBIFactory;
import io.dropwizard.setup.Environment;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.impl.StdSchedulerFactory;
import org.skife.jdbi.v2.DBI;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Created by vietnq2 on 11/24/15.
 */
public class LorealModule extends AbstractModule{

    protected void configure() {
        bind(AsyncService.class).to(AsyncServiceImpl.class);
        bindConstant().annotatedWith(Names.named("BaseFolder")).to("/tmp/");

    }

    @Provides
    public DBI provideDBI(LorealConfiguration configuration, Environment environment) {
        DBIFactory dbiFactory = new DBIFactory();
        DBI dbi = dbiFactory.build(environment, configuration.database, "mysql");
        return dbi;
    }

    @Provides
    public MailConfiguration provideMailConfiguration(LorealConfiguration configuration, Environment environment) {
        return configuration.mailConfiguration;
    }

    @Provides
    public FileConfiguration provideFileConfiguration(LorealConfiguration configuration, Environment environment) {
        return configuration.fileConfiguration;
    }

    @Provides
    public Scheduler scheduler(LorealConfiguration configuration, SchedulerJobFactory jobFactory) throws SchedulerException, IOException {
        Properties prop = new Properties();
//        prop.load(new FileInputStream(configuration.fileConfiguration.baseFolder + "/../quartz.properties"));
        prop.load(new FileInputStream(configuration.fileConfiguration.baseFolder + "/quartz.properties"));
        Scheduler scheduler = new StdSchedulerFactory(prop).getScheduler();
        scheduler.setJobFactory(jobFactory);
        scheduler.start();
        return scheduler;
    }

//    @Provides
//    public LorealConfiguration provideConfiguration(LorealConfiguration configuration, Environment environment) {
//        return configuration;
//    }

}
