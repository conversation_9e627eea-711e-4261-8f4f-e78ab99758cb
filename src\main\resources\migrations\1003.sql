--liquibase formatted sql
--changeset vietnq:1003
-- allow <EMAIL> full permission
INSERT INTO groups_permissions(id,group_id,permission_id,created) VALUES (7,1001,'*:*',1447653450436);

INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_SCI_SUBMISSION','Wait for sci submission','GLOBAL','notifications','status=\'WAIT_FOR_SCI_SUBMISSION\'',1455723614410);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN','Wait for SCI Director to assign','GLOBAL','notifications','status=\'WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN\'',1455723614411);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_SCI_VALIDATION','Wait for SCI validation','GLOBAL','notifications','status=\'WAIT_FOR_SCI_VALIDATION\'',1455723614412);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_CFS_INCI','Wait for CFS/INCI','GLOBAL','notifications','status=\'WAIT_FOR_CFS_INCI\'',1455723614413);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_CFS','Wait for CFS','GLOBAL','notifications','status=\'WAIT_FOR_CFS\'',1455723614414);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_INCI','Wait for INCI','GLOBAL','notifications','status=\'WAIT_FOR_INCI\'',1455723614415);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_SUBMISSION','Wait for submission','GLOBAL','notifications','status=\'WAIT_FOR_SUBMISSION\'',1455723614416);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_NOTIFICATION_NUMBER','Wait for notification number','GLOBAL','notifications','status=\'WAIT_FOR_NOTIFICATION_NUMBER\'',1455723614417);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_CANCEL','Wait for cancel','GLOBAL','notifications','status=\'WAIT_FOR_CANCEL\'',1455723614418);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('WAIT_FOR_REJECTION','Wait for rejection','GLOBAL','notifications','status=\'WAIT_FOR_REJECTION\'',1455723614419);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('REJECTED','Rejected','GLOBAL','notifications','status=\'REJECTED\'',1455723614420);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('CANCELLED','Cancelled','GLOBAL','notifications','status=\'CANCELLED\'',1455723614421);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('RECALLED','Recalled','GLOBAL','notifications','status=\'RECALLED\'',1455723614422);
INSERT INTO filters (id,name,scope,class_name,query,created) VALUES ('COMPLETED','Completed','GLOBAL','notifications','status=\'COMPLETED\'',1455723614423);

--rollback;