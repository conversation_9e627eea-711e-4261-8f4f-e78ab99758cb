package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Brand;
import com.jubiq.loreal.notifications.models.Range;
import com.jubiq.loreal.notifications.services.BrandService;
import com.jubiq.loreal.notifications.services.RangeService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq2 on 1/7/16.
 */
@Path("/api/ranges")
@Api("ranges")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class RangeEndpoint extends JubiqEndpoint<Long,Range> {
    private RangeService rangeService;
    private BrandService brandService;

    @Inject
    public RangeEndpoint(RangeService rangeService, BrandService brandService) {
        this.service = this.rangeService = rangeService;
        this.brandService = brandService;
    }

    @Override
    public Response doCreate(@Auth JubiqSession session, @ApiParam @Valid Range entity) throws UnauthorizedException, JubiqPersistenceException {
        Range range = rangeService.create(entity, session);
        return Response.ok(range).build();
    }
}
