package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;

import com.jubiq.loreal.notifications.models.Brand;
import com.jubiq.loreal.notifications.services.BrandService;
import com.wordnik.swagger.annotations.Api;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/5/16.
 */
@Path("/api/brands")
@Api("Brands")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class BrandEndpoint extends Ju<PERSON>qEndpoint<Long,Brand> {
    private BrandService brandService;
    @Inject
    public BrandEndpoint(BrandService brandService) {
        this.service = this.brandService = brandService;
    }
}
