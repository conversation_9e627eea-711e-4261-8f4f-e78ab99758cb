package com.jubiq.loreal.umgr.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.LorealConstants;
import com.jubiq.loreal.common.exceptions.*;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.notifications.services.ConfigurationService;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.models.UserPreference;
import io.dropwizard.auth.AuthenticationException;
import org.apache.commons.lang3.RandomStringUtils;
import org.mindrot.jbcrypt.BCrypt;

import java.util.*;

/**
 * Created by vietnq on 11/22/15.
 */
@Singleton
public class UserService extends JubiqService<Long,User>{

    private JubiqDao<String,JubiqSession> sessionDao;
    private UserPreferenceService preferenceService;
    private MailService mailService;
    private ConfigurationService configurationService;
    private GroupService groupService;

    @Inject
    public UserService(DaoFactory daoFactory, MailService mailService,
                       ConfigurationService configurationService,
                       GroupService groupService) {
        this.dao =  daoFactory.getUserDao();
        this.sessionDao = daoFactory.createDao(JubiqSession.class,String.class);
        this.groupService = groupService;
        this.mailService = mailService;
        this.configurationService = configurationService;
    }

    @Inject
    public void setPreferenceService(UserPreferenceService preferenceService) {
        this.preferenceService = preferenceService;
    }

    @Override
    public User create(User entity, JubiqSession session) throws JubiqPersistenceException {
        return super.create(entity, session);
    }

    @Override
    public void update(Long id, User entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        User existing = get(id);
        existing.avatarUri = entity.avatarUri;
        existing.email = entity.email;
        existing.fullName = entity.fullName;
        existing.groupId = entity.groupId;
        Group group = groupService.get(entity.groupId);
        existing.groupName = group.name;

        this.dao.update(existing);
        afterUpdate(id,existing);
    }

    public User findByEmail(String email) throws JubiqPersistenceException, EntityNotFoundException {
        String query = "select * from users where email=:email and deleted is null";
        Map<String,Object> bindMap = new HashMap<String, Object>();

        bindMap.put("email",email);

        List<User> users = this.dao.search(query,bindMap);
        if(users == null || users.size() == 0) {
            throw new EntityNotFoundException("No user with email " + email);
        }
        return users.get(0);
    }

    public JubiqSession createUserSession(String email, String password) throws JubiqPersistenceException, AuthenticationException, EntityNotFoundException {
        User user = findByEmail(email);
        if(user == null) {
            throw new AuthenticationException(new InvalidCredentialsException("Email or password is not correct"));
        }

        Boolean check = BCrypt.checkpw(password,user.password);
        if(!check) {
            throw new AuthenticationException(new InvalidCredentialsException("Email or password is not correct"));
        }
        JubiqSession session = new JubiqSession();
        session.userEmail = user.email;
        session.userId = user.id;
        session.expiresIn = Integer.valueOf(3600 * 8); //access token validated in 8h
        session.grantType = "client_credentials";
        session.fullName = user.fullName;
        Group group = groupService.get(user.groupId);
        session.groupName = group.name;
        session.groupId = group.id;
        session.permissions = groupService.groupPermissions(user.groupId);
        session = sessionDao.create(session);
        return session;
    }

    public void sendForgotPasswordEmail(Long userId) throws JubiqPersistenceException, EntityNotFoundException {
        User user = get(userId);
        if(user == null) {
            throw new EntityNotFoundException("No user with id " + userId);
        }
        user.forgotPwdToken = UUID.randomUUID().toString();
        user.forgotPwdTokenRequested = System.currentTimeMillis();
        this.dao.update(user);

        //send mail
        String mailContent = configurationService.getForgotPasswordEmail(userId,user.forgotPwdToken);
        mailService.sendMail(user.email,"Forgot Password",mailContent,null,null);
    }

    public Boolean changeForgottenPassword(Long userId, String newPassword, String token) throws JubiqPersistenceException, EntityNotFoundException, InvalidForgotPasswordTokenException, ForgotPasswordTokenExpiredException, InvalidRequestException {
        if(newPassword == null || newPassword.length() < 6) {
            throw new InvalidRequestException("Password must be 6 characters in minimum");
        }
        User user = get(userId);
        if(user == null) {
            throw new EntityNotFoundException("No user with id " + userId);
        }
        user.changeForgottenPassword(newPassword, token);
        user.clearForgottenPasswordToken();
        this.dao.update(user);
        return true;
    }

    public Boolean setPassword(Long userId, String newPassword) throws JubiqPersistenceException, EntityNotFoundException, InvalidRequestException {
        User user = get(userId);
        if(user == null) {
            throw new EntityNotFoundException("No user with id " + userId);
        }
        validatePassword(newPassword);
        user.setEncryptedPassword(newPassword);
        this.dao.update(user);
        return true;
    }

    public Boolean changePassword(Long userId, String currentPassword, String newPassword) throws JubiqPersistenceException, EntityNotFoundException, InvalidRequestException {
        User user = get(userId);
        if(user == null) {
            throw new EntityNotFoundException("No user with id " + userId);
        }
        validatePassword(newPassword);
        user.changePassword(currentPassword, newPassword);
        this.dao.update(user);
        return true;
    }

    @Override
    protected void afterCreate(User entity) throws JubiqPersistenceException {
        if(entity != null) {
            UserPreference userPreference = new UserPreference(entity.id);
            if(LorealConstants.SCI_MANAGER_GROUP_ID.equals(entity.groupId)) {
                userPreference.filters.add("WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN");
            } else if(LorealConstants.SCI_STAFF_GROUP_ID.equals(entity.groupId)) {
                userPreference.filters.add("WAIT_FOR_SCI_VALIDATION");
                userPreference.filters.add("WAIT_FOR_REJECTION");
                userPreference.filters.add("WAIT_FOR_CANCEL");
                userPreference.filters.add("WAIT_FOR_SUBMISSION");
            } else if(3000L <= entity.groupId && entity.groupId < 4000L) {
                //Marketing users
                userPreference.filters.add("WAIT_FOR_MKT_SUBMISSION");
                userPreference.filters.add("REJECTED");
                userPreference.filters.add("WAIT_FOR_NOTIFICATION_NUMBER");
                userPreference.filters.add("RECALLED");
            }
            preferenceService.create(userPreference, null);
        }
    }

    private void validatePassword(String password) throws InvalidRequestException {
        if(password == null || password.length() < 6) {
            throw new InvalidRequestException("Password must be 6 characters in minimum");
        }
    }

    public List<User> usersOfGroups(List<String> groupNames) {
        List<User> users = new ArrayList<User>();
        for(String groupName : groupNames) {
            Group group = groupService.findByName(groupName);
            users.addAll(usersByGroup(group.id));
        }
        return users;
    }

    public List<User> usersByGroup(Long groupId) {
        String sql = "select * from users where group_id=:groupId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("groupId",groupId);
        return this.dao.search(sql,map);
    }

    @Override
    protected void validateEntity(User entity) {
        Group group = groupService.getDao().get(entity.groupId);
        entity.groupName = group.name;
    }

    @Override
    public void afterUpdate(Long aLong, User entity) throws JubiqPersistenceException {
        updateNotification(aLong,entity);
        updateNote(aLong,entity);
        updateAlert(aLong,entity);
        updateHistory(aLong,entity);
        updateLabel(aLong,entity);
    }

    private void updateNotification(Long id, User user) {
        String[] types = new String[]{"assignee","follower","creator"};
        for(String type : types) {
            StringBuilder sb = new StringBuilder("update notifications set updated=:updated,");
            sb.append(type).append("_full_name=:fullName,");
            sb.append(type).append("_email=:email");
            sb.append(" where ").append(type).append("_id=:id");

            Map<String,Object> map = new HashMap<String, Object>();
            map.put("updated",System.currentTimeMillis());
            map.put("fullName",user.fullName);
            map.put("email",user.email);
            map.put("id",id);
            this.dao.executeUpdate(sb.toString(),map);
        }

    }

    private void updateNote(Long id, User user) {
        String sql = "update notes set creator_full_name=:fullName,updated=:updated where creator_id=:id";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("fullName",user.fullName);
        map.put("id",id);
        this.dao.executeUpdate(sql,map);
    }

    private void updateAlert(Long id, User user) {
        String sql = "update alerts set source_full_name=:fullName,updated=:updated where source_id=:id";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("fullName",user.fullName);
        map.put("id",id);
        this.dao.executeUpdate(sql,map);
    }

    private void updateHistory(Long id, User user) {
        String sql = "update histories set actor_full_name=:fullName,updated=:updated where actor_id=:id";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("fullName",user.fullName);
        map.put("id",id);
        this.dao.executeUpdate(sql,map);
    }

    private void updateLabel(Long id, User user) {
        String[] types = new String[]{"assignee","follower","creator"};
        for(String type : types) {
            StringBuilder sb = new StringBuilder("update labels set updated=:updated,");
            sb.append(type).append("_full_name=:fullName,");
            sb.append(type).append("_email=:email");
            sb.append(" where ").append(type).append("_id=:id");

            Map<String,Object> map = new HashMap<String, Object>();
            map.put("updated",System.currentTimeMillis());
            map.put("fullName",user.fullName);
            map.put("email",user.email);
            map.put("id",id);
            this.dao.executeUpdate(sb.toString(),map);
        }

    }

    @Override
    public void beforeCreate(User entity) throws JubiqPersistenceException {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        String password = RandomStringUtils.random( 8, characters );
        entity.setEncryptedPassword(password);
        //send email
        String mailContent = configurationService.getCreateUserEmail(password);
        mailService.sendMail(entity.email,"Password",mailContent,null,null);
        super.beforeCreate(entity);
    }
}
