package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.ExporterFactory;
import com.jubiq.loreal.notifications.services.ExporterFactoryService;
import com.jubiq.loreal.notifications.services.FactoryService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by ihb on 23/06/2025.
 */
@Path("/api/exporter-factories")
@Api("Exporter Factories")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class ExporterFactoryEndpoint extends JubiqEndpoint<Long,ExporterFactory>{
    private ExporterFactoryService factoryService;
    @Inject
    public ExporterFactoryEndpoint(ExporterFactoryService factoryService) {
        this.service = this.factoryService = factoryService;
    }

    @GET
    @Path("/country-exporter-factories")
    @ApiOperation("Get exporter factories by country")
    public Response factoriesByCountry(@Auth JubiqSession session, @QueryParam("countryId") Long countryId) {
        List<ExporterFactory> factories = this.factoryService.factoriesOfCountry(countryId);
        return Response.ok(factories).build();
    }
}
