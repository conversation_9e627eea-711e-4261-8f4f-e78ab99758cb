package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 12/21/15.
 */
public class InvalidCredentialsException extends JubiqException {
    public InvalidCredentialsException(Throwable e) {
        super(e);
    }

    public InvalidCredentialsException(String message) {
        super(message);
    }

    public InvalidCredentialsException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.INVALID_CREDENTIALS.name();
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.INVALID_CREDENTIALS.httpStatus;
    }
}
