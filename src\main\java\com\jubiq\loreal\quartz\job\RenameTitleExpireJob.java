package com.jubiq.loreal.quartz.job;

import com.google.inject.Inject;
import com.jubiq.loreal.notifications.services.FilterService;
import com.jubiq.loreal.notifications.services.NotificationService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

public class RenameTitleExpireJob implements Job {
  @Inject
  private FilterService filterService;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) {
    filterService.executeRenameTitleExpireJob();
  }
}
