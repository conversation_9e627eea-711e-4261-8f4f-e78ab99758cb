package com.jubiq.loreal.notifications.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * Created by vietnq2 on 1/7/16.
 */
public class Range extends JubiqEntity<Long>{
    @NotEmpty
    public String name;
    @NotNull
    public Long brandId;

    @NotAColumn
    public Brand brand;
}
