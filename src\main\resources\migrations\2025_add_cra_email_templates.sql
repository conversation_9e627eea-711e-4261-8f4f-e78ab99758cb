--liquibase formatted sql
--changeset ai-assistant:2025

-- Email template for GM approval
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) 
VALUES ('cra-approval-gm.email.template', 
        'Dear General Manager,<br><br>CRA Request %d (%s) has completed risk assessment and requires your approval.<br><br>Please click <a href="%s">here</a> to review and approve/reject this request.<br><br>This link will automatically log you in for quick approval.<br><br>Best regards,<br>L''Oreal CRA System', 
        'Email template for GM CRA approval', 
        NULL, NULL, NULL, NULL);

-- Email template for CM approval
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('cra-approval-cm.email.template',
        'Dear Category Manager,<br><br>CRA Request %d (%s) has completed risk assessment and requires your approval.<br><br>Please click <a href="%s">here</a> to review and approve/reject this request.<br><br>This link will automatically log you in for quick approval.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CM CRA approval',
        NULL, NULL, NULL, NULL);

-- GM email configuration
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('cra.gm.email',
        '<EMAIL>',
        'Email address for General Manager CRA approvals',
        NULL, NULL, NULL, NULL);

-- CM email configuration
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('cra.cm.email',
        '<EMAIL>',
        'Email address for Category Manager CRA approvals',
        NULL, NULL, NULL, NULL);

--rollback DELETE FROM configurations WHERE id IN ('cra-approval-gm.email.template', 'cra-approval-cm.email.template', 'cra.gm.email', 'cra.cm.email');
