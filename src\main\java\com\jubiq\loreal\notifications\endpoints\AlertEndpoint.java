package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.services.AlertService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/21/16.
 */
@Path("/api/alerts")
@Api("Alerts")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class AlertEndpoint extends JubiqEndpoint<Long,Alert> {
    private AlertService alertService;
    @Inject
    public AlertEndpoint(AlertService alertService) {
        this.service = this.alertService = alertService;
    }

    @GET
    @Path("/query")
    @ApiOperation("Query alerts by user")
    public Response query(@Auth JubiqSession session, @QueryParam("userId") Long userId,@QueryParam("topRecent") Integer topRecent) {
        checkOwner(session,userId);
        List<Alert> alerts = alertService.recentUnread(userId,topRecent);
        return Response.ok(alerts).build();
    }

    @GET
    @Path("/unread-count")
    @ApiOperation("Query alerts by user")
    public int countUnRead(@Auth JubiqSession session, @QueryParam("userId") Long userId) {
        int count = alertService.countUnRead(userId);
        return count;
    }

    @POST
    @Path("/{id}/read")
    @ApiOperation("Read an alert")
    public Response read(@Auth JubiqSession session, @PathParam("id") Long id) {
        Alert alert = alertService.get(id);
        checkOwner(session,alert.targetId);
        this.alertService.read(id);
        return Response.ok().build();
    }

    @POST
    @Path("/read")
    @ApiOperation("Read a list of alerts")
    public Response read(@Auth JubiqSession session, List<Long> ids) {
        this.alertService.batchRead(ids);
        return Response.ok().build();
    }
}
