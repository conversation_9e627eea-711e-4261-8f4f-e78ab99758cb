package com.jubiq.loreal.cra.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.services.QuickLoginService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by AI Assistant on 7/9/25.
 * REST endpoint for Quick Login functionality
 */
@Path("/api/cra-quick-login")
@Api("CRA Quick Login")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Singleton
public class QuickLoginEndpoint {
    
    @Inject
    private QuickLoginService quickLoginService;
    
    @POST
    @Path("/authenticate")
    @ApiOperation("Authenticate using quick login hash")
    public Response authenticateQuickLogin(@ApiParam @FormParam("craRequestId") Long craRequestId,
                                          @ApiParam @FormParam("loginHash") String loginHash,
                                          @ApiParam @FormParam("email") String email) {
        try {
            // Validate parameters
            if (craRequestId == null || loginHash == null || email == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                             .entity("Missing required parameters: craRequestId, loginHash, email")
                             .build();
            }

            // Basic email validation
            if (!email.contains("@") || !email.contains(".")) {
                return Response.status(Response.Status.BAD_REQUEST)
                             .entity("Invalid email format")
                             .build();
            }
            
            // Authenticate and create session
            JubiqSession session = quickLoginService.authenticateQuickLogin(craRequestId, loginHash, email);
            
            return Response.ok(session).build();
            
        } catch (EntityNotFoundException e) {
            return Response.status(Response.Status.NOT_FOUND)
                         .entity(e.getMessage())
                         .build();
        } catch (InvalidRequestException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                         .entity(e.getMessage())
                         .build();
        } catch (JubiqPersistenceException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                         .entity("Database error occurred")
                         .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                         .entity("An unexpected error occurred")
                         .build();
        }
    }
    
    @GET
    @Path("/validate")
    @ApiOperation("Validate quick login session")
    public Response validateQuickLogin(@QueryParam("sessionId") String sessionId,
                                      @QueryParam("craRequestId") Long craRequestId) {
        try {
            // This endpoint can be used to validate if a session is valid for quick login
            // Implementation would check session validity and CRA request access
            
            return Response.ok().entity("Session validation endpoint").build();
            
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                         .entity("Validation failed")
                         .build();
        }
    }
}
