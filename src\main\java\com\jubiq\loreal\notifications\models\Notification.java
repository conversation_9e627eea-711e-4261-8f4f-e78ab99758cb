package com.jubiq.loreal.notifications.models;


import com.jubiq.loreal.advertisings.models.Advertising;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.notifications.models.enumerations.NotificationStatus;
import com.jubiq.loreal.notifications.models.enumerations.Presentation;
import com.jubiq.loreal.notifications.util.Utils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by vietnq2 on 11/25/15.
 */
public class Notification extends JubiqEntity<Long> {
    public Presentation presentationType;
    public NotificationStatus status;
    public String productName;
    public Long brandId;
    public String brandName;
    public Long productRangeId;
    public String productRangeName;
    //public Long productTypeId;
    @NotAColumn
    public String productTypeDesc;
    public List<Long> productTypeIds;
    @Serialized
    public Map<String,String> awFiles;
    @Serialized
    public Map<String,String> launchFormFiles;
    public Date launchTime;
    public Date shipmentRequestTime;
    public String intendedUse;
    public Date validatedTime;
    public Date cfsRequestingDate;
    public Boolean cfsAvailable;
    public Date cfsReceivingDate;
    @Serialized
    public Map<String,String> cfsFiles;
    public Date inciRequestingDate;
    public Boolean inciAvailable;
    public Date inciReceivingDate;
    @Serialized
    public Map<String,String> inciFiles;
    public String davNotificationNumber;
    public Date davRequestingDate;
    public Date davOfficialRequestingDate;
    public Date davReceivingDate;
    public Date davExpiringDate;
    public Boolean davAvailable;
    @Serialized
    public Map<String,String> davFiles;
    public Long assigneeId;
    public String assigneeEmail;
    public String assigneeFullName;
    public Long followerId;
    public String followerFullName;
    public String followerEmail;
    public String creatorEmail;
    public String creatorFullName;
    public Boolean validated;
    public Long groupId;
    public String productNameVn;
    public String otherPresentationType;
    public Date submittingDate;
    public Date revisingDate;
    public String warning;
    public Boolean rejectProductName;
    public String notiType;
    public String reasonUrgent;
    public String contentReasonOther;
    public Date scValidateDate;
    public Boolean isRenoti;
    public Date medicalDeviceDefinedDate;
    @NotAColumn
    public List<PresentationDetail> presentationDetails;

    /**
     * Dung trong tinh nang xuat du lieu noti co quang cao hay khong
     */
    @NotAColumn
    public List<Advertising> advertising;

    @NotAColumn
    public Boolean hasLabel;

    /**
     * Chua cac notification co trung fomula_number, fill_code, englist_product_name dung cho man hinh danh sach noti
     */
    @NotAColumn
    public Map<Long, String> duplicateNotifications;

    /**
     * Map chua key la cac gia tri bi duplicate va value la id cua noti bi duplicate ( productname)
     */
    @NotAColumn
    public Map<String, Set<Long>> mapDuplicateNotification;

    public Notification() {
        presentationDetails = new ArrayList<PresentationDetail>();
        advertising = new ArrayList<Advertising>();
        duplicateNotifications = new HashMap<Long, String>();
        mapDuplicateNotification = new HashMap<String, Set<Long>>();
        awFiles = new HashMap<String,String>();
        launchFormFiles = new HashMap<String,String>();
        cfsFiles = new HashMap<String,String>();
        inciFiles = new HashMap<String,String>();
        cfsAvailable = false;
        inciAvailable = false;
        validated = false;
        davAvailable = false;
    }
    public Set<String> compareFields(Notification other) {
        Set<String> differentFields = new HashSet<>();
        Class<?> clazz = getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value1 = field.get(this);
                Object value2 = field.get(other);
                if(value1 == null && value2 == null) continue;
                if(field.getName().equals("mapDuplicateNotification")
                        || field.getName().equals("duplicateNotifications")
                        || field.getName().equals("advertising")
                        || field.getName().equals("productTypeIds")
                        || field.getName().equals("status")
                        || field.getName().equals("brandId")
                        || field.getName().equals("productRangeId")
                        || field.getName().equals("cfsAvailable")
                        || field.getName().equals("davAvailable")
                        || field.getName().equals("assigneeId")
                        || field.getName().equals("assigneeFullName")
                        || field.getName().equals("followerId")
                        || field.getName().equals("creatorFullName")
                        || field.getName().equals("validated")
                        || field.getName().equals("groupId")
                        || field.getName().equals("submittingDate")
                        || field.getName().equals("revisingDate")
                        || field.getName().equals("hasLabel")
                        || field.getName().equals("followerFullName")
                        || field.getName().equals("scValidateDate")
                        || field.getName().equals("isRenoti")

                ) continue;
                if(value1 instanceof Map && value2 instanceof Map && (((Map)value1).equals((Map)value2))){
                    continue;
                }
                if(value1 instanceof Date && value2 instanceof Date && Utils.convertDate((Date)value1).equals(Utils.convertDate((Date)value2))){
                    continue;
                }
                if (value1 != null && value2 == null || value1 == null && value2 != null || !value1.equals(value2)) {
                    if(field.getName().equals("presentationDetails")){
                        List<PresentationDetail> list = (List<PresentationDetail>) value1;
                        int sizeOld = differentFields.size();
                        for(PresentationDetail p : list) differentFields.addAll(p.differProperties);
                        int sizeNew = differentFields.size();
                        if(sizeOld==sizeNew) differentFields.add("Packaging");
                    }
                    else {
                        differentFields.add(Utils.lowerCaseToCamelCase(getFieldName(field.getName())));
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return differentFields;
    }
    public String getFieldName(String field){
        String fieldReturn = field;
        switch (field){
            case "productRangeName":
                fieldReturn = "Range";
                break;
            case "productTypeDesc":
                fieldReturn = "ProductType";
                break;
        }
        return fieldReturn;
    }
}
