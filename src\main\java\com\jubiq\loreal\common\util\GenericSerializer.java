package com.jubiq.loreal.common.util;


import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

public class GenericSerializer implements Serializer<Object> {

    private ObjectMapper mapper;

    public GenericSerializer() {
        mapper = new ObjectMapper();
    }

    @Override
    public Object deSerialize(Class type, String value) throws IOException {
        return mapper.readValue(value, type);
    }

    @Override
    public String serialize(Object value) throws Exception {
        return mapper.writeValueAsString(value);
    }

    @Override
    public Object deSerializeItem(String value) throws IOException {
        return mapper.readValue(value, String.class);
    }

    @Override
    public String serializeItem(Object value) throws Exception {
        return mapper.writeValueAsString(value);
    }
}
