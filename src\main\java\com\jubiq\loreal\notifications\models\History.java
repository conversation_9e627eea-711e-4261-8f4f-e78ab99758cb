package com.jubiq.loreal.notifications.models;

import com.jubiq.loreal.common.model.JubiqEntity;

/**
 * Created by vietnq2 on 1/21/16.
 */
public class History extends JubiqEntity<Long> {
    public Long objectId;
    public String objectName;
    public String action;
    public Long actorId;
    public String actorFullName;
    public String oldValue;
    public String newValue;
    public String reason;

    public History() {

    }

    public History(Long objectId, String objectName, String action, Long actorId, String actorFullName, String oldValue, String newValue, String reason) {
        this.objectId = objectId;
        this.objectName = objectName;
        this.action = action;
        this.actorId = actorId;
        this.actorFullName = actorFullName;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.reason = reason;
    }
}
