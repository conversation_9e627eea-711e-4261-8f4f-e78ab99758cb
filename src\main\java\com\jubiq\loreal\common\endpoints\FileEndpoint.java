package com.jubiq.loreal.common.endpoints;

/**
 * Created by vietnq on 12/26/15.
 */

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqFile;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.service.FileService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import java.nio.file.Files;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import static javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;

@Path("/api/files")
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED,MULTIPART_FORM_DATA})
@Produces(MediaType.APPLICATION_JSON)
@Api("files")
@Singleton
public class FileEndpoint extends JubiqEndpoint<Long,JubiqFile> {
    private static Logger LOGGER = LoggerFactory.getLogger(FileEndpoint.class);
    private FileService fileService;

    @Inject
    public FileEndpoint(FileService fileService) {
        this.service = this.fileService = fileService;
    }

    @POST
    @Path("/upload")
    @ApiOperation("Upload a file")
    public Response uploadFile(@Auth JubiqSession session,
            @ApiParam @FormDataParam("file") InputStream uploadedInputStream,
            @ApiParam @FormDataParam("file") FormDataContentDisposition fileDetail) throws IOException, JubiqPersistenceException, EntityNotFoundException {
        JubiqFile file = fileService.writeToFile(uploadedInputStream,fileDetail.getFileName());
        return Response.ok(file).build();
    }

    @GET
    @Path("/download/{fileId}")
    @ApiOperation("Download a file")
    public Response downloadFile(@ApiParam @PathParam("fileId") Long fileId) throws JubiqPersistenceException {
      JubiqFile jubiqFile = fileService.get(fileId);
        File file = new File(jubiqFile.uri);

        // Tự động xác định content-type
        String mimeType;
        try {
            mimeType = Files.probeContentType(file.toPath());
            if (mimeType == null) {
                String name = jubiqFile.name.toLowerCase();
                if (name.endsWith(".pdf")) mimeType = "application/pdf";
                else if (name.endsWith(".doc")) mimeType = "application/msword";
                else if (name.endsWith(".docx")) mimeType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                else if (name.endsWith(".xls")) mimeType = "application/vnd.ms-excel";
                else if (name.endsWith(".xlsx")) mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                else mimeType = "application/octet-stream"; // fallback
            }
        } catch (IOException e) {
            mimeType = "application/octet-stream";
        }

        // response builder
        Response.ResponseBuilder response = Response.ok(file);
        response.header("Content-Type", mimeType);

        // Nếu là file có thể xem trực tiếp (pdf, image, office...), dùng inline
        boolean isViewable = mimeType.startsWith("image/")
                        || mimeType.equals("application/pdf")
                        || mimeType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") // .docx
                        || mimeType.equals("application/msword") // .doc
                        || mimeType.equals("application/vnd.ms-excel") // .xls
                        || mimeType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // .xlsx

        if (isViewable) {
            response.header("Content-Disposition", "inline; filename=\"" + jubiqFile.name + "\"");
        } else {
            response.header("Content-Disposition", "attachment; filename=\"" + jubiqFile.name + "\"");
        }

        return response.build();
    }
}
