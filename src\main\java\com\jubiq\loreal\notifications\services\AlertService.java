package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.service.AsyncService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/21/16.
 */
@Singleton
public class AlertService extends JubiqService<Long,Alert> {
    private AsyncService asyncService;
    private JubiqDao<Long,User> userDao;
    private ConfigurationService configurationService;
    private MailService mailService;
    private UserService userService;
    @Inject
    public AlertService(DaoFactory daoFactory,
                        AsyncService asyncService,
                        MailService mailService,
                        ConfigurationService configurationService,
                        UserService userService) {
        this.dao = daoFactory.createDao(Alert.class,Long.class);
        this.asyncService = asyncService;
        this.userDao = daoFactory.createDao(User.class,Long.class);
        this.mailService = mailService;
        this.configurationService = configurationService;
        this.userService = userService;
    }

    public List<Alert> findByUser(Long userId) {
        StringBuilder sb = new StringBuilder("select * from alerts where ");
        sb.append("target_id=:userId and deleted is null and read_at is not null ");
        sb.append("order by created desc");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("userId",userId);
        return this.dao.search(sb.toString(),map);
    }

    public int countUnRead(Long userId) {
        String sql = "read_at is null and target_id=" + userId;
        return this.dao.count(sql);
    }

    public List<Alert> recentUnread(Long userId,Integer topRecent) {
        String sql = "select * from alerts where deleted is null and read_at is null and target_id=:userId order by created desc";
        if(topRecent != null) {
            sql+=" limit " + topRecent;
        }
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("userId",userId);
        return this.dao.search(sql,map);
    }

    public void read(Long id) {
        String sql = "update alerts set read_at=:read where id=:id";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("read",System.currentTimeMillis());
        map.put("id",id);
        this.dao.executeUpdate(sql,map);
    }

    public void batchRead(List<Long> ids) {
        for(Long id : ids) {
            read(id);
        }
    }

    public void createAlert(Alert alert, Long targetGroupId, Long targetUserId) {
        CreateAlertTask task = new CreateAlertTask();
        task.targetGroupId = targetGroupId;
        task.targetUserId = targetUserId;
        task.alert = alert;
        this.asyncService.execute(task);
    }

    public List<User> usersByGroup(Long groupId) {
        String sql = "select * from users where group_id=:groupId and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("groupId",groupId);
        return this.userDao.search(sql, map);
    }

    @Override
    protected void afterCreate(Alert entity) throws JubiqPersistenceException {
        sendAlertEmail(entity);
    }

    private void sendAlertEmail(Alert alert) {
        String emailTemplate = configurationService.getAlertEmail(alert.action);
        String recipient = null;
        if(alert.targetId != null) {
            try {
                recipient = userService.get(alert.targetId).email;
            } catch(Exception e) {
                //no target id, no recipient
            }
        }

        if(emailTemplate != null && emailTemplate.length() > 0 && recipient != null) {
            //send alert email
            String body = emailTemplate;
            body = body.replace("${sourceFullName}",alert.sourceFullName);
            for(Map.Entry<String,String> entry : alert.params.entrySet()) {
                String placeHolder = String.format("${%s}",entry.getKey());
                if(emailTemplate.contains(entry.getKey())) {
                    body = body.replace(placeHolder,entry.getValue());
                }
            }
            mailService.sendMail(recipient,"Notification Tools",body,null,null);
        }
    }

    private class CreateAlertTask implements Runnable {
        public Long targetGroupId;
        public Alert alert;
        public Long targetUserId;
        @Override
        public void run() {
            if(targetGroupId != null) {
                List<User> targetUsers = AlertService.this.usersByGroup(targetGroupId);
                for(User user : targetUsers) {
                    alert.id = null;
                    if((alert.sourceId == null && alert.sourceFullName.equals("Notification: ")) || !alert.sourceId.equals(user.id)) {
                        alert.targetId = user.id;
                        AlertService.this.create(alert,null);
                    }
                }
            }
            if(targetUserId != null) {
                alert.id = null;
                if(!alert.sourceId.equals(targetUserId)) {
                    alert.targetId = targetUserId;
                    AlertService.this.create(alert,null);
                }
            }
        }
    }
}
