package com.jubiq.loreal.common.exceptions;

import static javax.ws.rs.core.Response.Status;

/**
 * Created by vietnq2 on 12/18/15.
 */
public enum JubiqErrorType {
    UNKNOWN(Status.INTERNAL_SERVER_ERROR),
    AUTHENTICATION(Status.UNAUTHORIZED),
    INVALID_CREDENTIALS(Status.UNAUTHORIZED),
    SESSION_NOT_FOUND(Status.UNAUTHORIZED),
    SESSION_EXPIRED(Status.UNAUTHORIZED),
    JUBIQ_PERSISTENCE(Status.INTERNAL_SERVER_ERROR),
    ALREADY_EXIST(Status.BAD_REQUEST),
    ENTITY_NOT_FOUND(Status.NOT_FOUND),
    UNAUTHORIZED(Status.UNAUTHORIZED),
    INVALID_REQUEST(Status.BAD_REQUEST),
    VALIDATION_FAILED(Status.fromStatusCode(422)),
    INVALID_FORGOT_PWD_TOKEN(Status.BAD_REQUEST),
    FORGOT_PWD_TOKEN_EXPIRED(Status.BAD_REQUEST),
    INVALID_OWNER_RESOURCE(Status.UNAUTHORIZED);


    public Status httpStatus;

    JubiqErrorType(Status httpStatus) {
        this.httpStatus = httpStatus;
    }
}
