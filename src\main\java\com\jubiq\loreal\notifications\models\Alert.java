package com.jubiq.loreal.notifications.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.Serialized;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by vietnq2 on 1/20/16.
 */
public class Alert extends JubiqEntity<Long> {
    public Long sourceId;
    public String sourceFullName;
    public Long targetId;
    public Long readAt;
    public String action;
    @Serialized
    public Map<String,String> params;

    public Alert() {
        params = new HashMap<String,String>();
    }

    public Alert(Long sourceId,String sourceFullName, String action) {
        this.sourceId = sourceId;
        this.sourceFullName = sourceFullName;
        this.action = action;
        this.params = new HashMap<String, String>();
    }
}
