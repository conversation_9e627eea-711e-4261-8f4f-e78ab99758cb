package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Ingredient;
import com.jubiq.loreal.notifications.models.PresentationDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 1/18/16.
 */
@Singleton
public class IngredientService extends JubiqService<Long,Ingredient> {

    private static final Logger LOGGER = LoggerFactory.getLogger(IngredientService.class);
    private JubiqDao<Long,PresentationDetail> presentationDetailDao;
    @Inject
    public IngredientService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(Ingredient.class,Long.class);
        this.presentationDetailDao = daoFactory.createDao(PresentationDetail.class,Long.class);
    }

    public List<Ingredient> findByFLAandFIL(String formulaNumber, String filCode) {
        String sql = "select distinct * from ingredients where formula_number=:formulaNumber and fil_code=:filCode and deleted is null order by order_number asc";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("formulaNumber",formulaNumber);
        map.put("filCode",filCode);
        return this.dao.search(sql,map);
    }

    public void deleteByFLAandFIL(String formulaNumber,String filCode) {
        String sql = "delete from ingredients where formula_number=:formulaNumber and fil_code=:filCode";
        LOGGER.info("delete ingredient, fla: {}, filCode: {}",formulaNumber,filCode);
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("formulaNumber",formulaNumber);
        map.put("filCode",filCode);
        this.dao.executeUpdate(sql,map);
    }

    public Ingredient findByPK(String formulaNumber, String filCode, String fullName) {
        String sql = "select distinct * from ingredients where formula_number=:formulaNumber and fil_code=:filCode";
        sql += " and full_name=:fullName";
        sql += " and deleted is null order by order_number asc";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("formulaNumber",formulaNumber);
        map.put("filCode",filCode);
        map.put("fullName",fullName);
        List<Ingredient> ingredients = this.dao.search(sql,map);
        if(ingredients == null || ingredients.size() == 0) {
            throw new EntityNotFoundException("No ingredient found with fla: " + formulaNumber + " ,filCode: " + filCode + " ,name: " + fullName);
        }
        return ingredients.get(0);
    }

    public void importIngredient(InputStream is,JubiqSession session) throws IOException {
        Workbook wb = new XSSFWorkbook(is);
        Sheet sheet = wb.getSheetAt(0);
        Map<Integer,String> fieldMap = new HashMap<Integer, String>();
        Iterator<Row> rowIterator = sheet.iterator();
        Map<String,List<Ingredient>> ingredientMap = new HashMap<String,List<Ingredient>>();

        while(rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Iterator<Cell> cellIterator = row.iterator();
            if (row.getRowNum() == 0) {
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    fieldMap.put(cell.getColumnIndex(), cell.getStringCellValue().trim());
                }
            } else {
                Ingredient ingredient = new Ingredient();
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    String fieldName = fieldMap.get(cell.getColumnIndex());
                    if ("Formula Number".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.formulaNumber = cell.getStringCellValue();
                    } else if ("Filcode".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.filCode = cell.getStringCellValue();
                    } else if ("No".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_NUMERIC);
                        ingredient.orderNumber = (int) cell.getNumericCellValue();
                    } else if ("Full Ingredient name".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.fullName = cell.getStringCellValue();
                    } else if ("Percentages of restricted ingredients".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.percentage = cell.getStringCellValue();
                    } else if ("Note".equals(fieldName)) {
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        ingredient.note = cell.getStringCellValue();
                    }
                }
                if(!StringUtils.isBlank(ingredient.formulaNumber) && !StringUtils.isBlank(ingredient.filCode)) {
                  String key = ingredient.formulaNumber + "JUBIQ" + ingredient.filCode;
                  if (ingredientMap.get(key) == null) {
                    ingredientMap.put(key, new ArrayList<Ingredient>());
                  }
                  ingredientMap.get(key).add(ingredient);
                }
            }
        }
        for(String key : ingredientMap.keySet()) {
            List<Ingredient> ingredients = ingredientMap.get(key);
            String[] tmp = key.split("JUBIQ");
            deleteByFLAandFIL(tmp[0],tmp[1]);
            for(Ingredient ingredient : ingredients) {
                Long currentId = currentIncrementValue();
                ingredient.id = currentId;
                this.dao.create(ingredient);
            }
            updatePresentationDetail(tmp[0],tmp[1],ingredients);
        }
        wb.close();
        is.close();
    }

    private void updatePresentationDetail(String formulaNumber,String filCode, List<Ingredient> ingredients) {
        String sql = "select * from presentation_details where formula_number=:formulaNumber and fil_code=:filCode and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("formulaNumber",formulaNumber);
        map.put("filCode",filCode);
        List<PresentationDetail> details = this.presentationDetailDao.search(sql,map);
        for(PresentationDetail detail : details) {
            LOGGER.info("update ingredients of presentation detail #{}",detail.id);
            detail.ingredients = ingredients;
            this.presentationDetailDao.update(detail);
        }
    }

    private Long currentIncrementValue() {
      String sql = "select auto_increment from information_schema.TABLES where TABLE_SCHEMA='loreal' and TABLE_NAME='ingredients'";
      return this.dao.selectLong(sql);
    }
}
