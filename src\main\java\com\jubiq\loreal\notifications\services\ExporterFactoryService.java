package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Country;
import com.jubiq.loreal.notifications.models.ExporterFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ihb on 23/06/2025.
 */
@Singleton
public class ExporterFactoryService extends JubiqService<Long,ExporterFactory> {
    private CountryService countryService;
    @Inject
    public ExporterFactoryService(DaoFactory daoFactory, CountryService countryService) {
        this.dao = daoFactory.createDao(ExporterFactory.class, Long.class);
        this.countryService = countryService;
    }

    public ExporterFactory findByName(String name) {
        String sql = "select * from exporter_factories where name=:name and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("name",name);
        List<ExporterFactory> factories = this.dao.search(sql,map);
        if(factories != null && factories.size() > 0) {
            return factories.get(0);
        } else {
            throw new EntityNotFoundException("No expoter factory found with name: " + name);
        }
    }

    public List<ExporterFactory> factoriesOfCountry(final Long countryId) {
        String sql = "select * from exporter_factories where country_id=:countryId and deleted is null order by name desc";
        Map<String,Object> map = new HashMap<String, Object>(){{put("countryId",countryId);}};
        List<ExporterFactory> factories = this.dao.search(sql,map);
        return factories;
    }

    @Override
    protected void validateEntity(ExporterFactory factory) {
        Country country = countryService.get(factory.countryId);
        factory.countryName = country.name;
    }

    @Override
    public void afterUpdate(Long id,ExporterFactory entity) throws JubiqPersistenceException {
        updateExporterInfo(entity);
    }

    private void updateExporterInfo(ExporterFactory entity) {
        StringBuilder sb = new StringBuilder("update presentation_details ");
        sb.append("set exporter_name=:exporterName")
                .append(",exporter_address=:exporterAddress")
                .append(",exporter_phone_number=:exporterPhoneNumber")
                .append(",exporter_fax_number=:exporterFaxNumber")
                .append(",exporter_country_id=:exporterCountryId")
                .append(",exporter_country_name=:exporterCountryName")
                .append(",updated=:updated")
                .append(" where exporter_id=:exporterId and deleted is null");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("exporterName",entity.name);
        map.put("exporterAddress",entity.address);
        map.put("exporterPhoneNumber",entity.phoneNumber);
        map.put("exporterFaxNumber",entity.faxNumber);
        map.put("exporterCountryId",entity.countryId);
        map.put("exporterCountryName",entity.countryName);
        map.put("updated",System.currentTimeMillis());
        map.put("exporterId",entity.id);
        this.dao.executeUpdate(sb.toString(),map);
    }
}
