package com.jubiq.loreal.advertisings.services;

import com.jubiq.loreal.advertisings.models.AdvertisingXlsTemplate;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;

import javax.inject.Inject;

public class AdvertisingXlsTemplateService extends JubiqService<Long,AdvertisingXlsTemplate> {
    @Inject
    public AdvertisingXlsTemplateService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(AdvertisingXlsTemplate.class,Long.class);
    }
}