--liquibase formatted sql
--changeset vietnq:008

ALTER TABLE brands DROP COLUMN ranges;
ALTER TABLE notifications DROP COLUMN product_range;
ALTER TABLE notifications ADD product_range_id INTEGER;
ALTER TABLE notifications ADD product_range_name <PERSON><PERSON><PERSON><PERSON>(255);

CREATE TABLE ranges (
  id INTEGER NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  brand_id INTEGER,
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  CONSTRAINT uniq_range_name_in_brand UNIQUE(brand_id,name),
  PRIMARY KEY (id)
);
--rollback;