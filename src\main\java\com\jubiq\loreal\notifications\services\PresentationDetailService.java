package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.ExporterFactory;
import com.jubiq.loreal.notifications.models.Factory;
import com.jubiq.loreal.notifications.models.PresentationDetail;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 1/18/16.
 */
@Singleton
public class PresentationDetailService extends JubiqService<Long,PresentationDetail> {
    private FactoryService factoryService;
    private ExporterFactoryService exporterFactoryService;
    private IngredientService ingredientService;
    @Inject
    public PresentationDetailService(DaoFactory daoFactory,
                                     FactoryService factoryService,
                                     ExporterFactoryService exporterFactoryService,
                                     IngredientService ingredientService) {
        this.dao = daoFactory.createDao(PresentationDetail.class, Long.class);
        this.factoryService = factoryService;
        this.ingredientService = ingredientService;
        this.exporterFactoryService = exporterFactoryService;
    }

    public List<PresentationDetail> findByNotificationId(Long notificationId) {
        String sql = "select * from presentation_details where notification_id=:notificationId and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("notificationId",notificationId);
        return this.dao.search(sql,map);
    }
    public List<PresentationDetail> findByIdIn(String ids) {
        String sql = "select * from presentation_details where id in " + ids + "and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        return this.dao.search(sql,map);
    }
    public List<Long> findByFormula(Long notificationId,String formulaNumber ) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String toDay = dateFormat.format(Calendar.getInstance().getTime());
        /* String sql = "select notification_id from presentation_details where deleted is null and formula_number=:formulaNumber and notification_id <> " + notificationId +
                " and notification_id not in (select id from notifications where dav_expiring_date <= '" + toDay + "' )"; */
        String sql = "select notification_id from presentation_details where deleted is null and formula_number=:formulaNumber and notification_id <> " + notificationId;
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("formulaNumber",formulaNumber);
        return this.dao.selectLongID(sql,map);
    }
    public List<Long> findByFillCode(Long notificationId,String filCode ) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String toDay = dateFormat.format(Calendar.getInstance().getTime());
        /* String sql = "select notification_id from presentation_details where deleted is null and fil_code=:filCode and notification_id <> " + notificationId +
                " and notification_id not in (select id from notifications where dav_expiring_date <= '" + toDay + "' )"; */
        String sql = "select notification_id from presentation_details where deleted is null and fil_code=:filCode and notification_id <> " + notificationId;
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("filCode",filCode);
        return this.dao.selectLongID(sql,map);
    }
    public List<Long> findByShadeName(Long notificationId,String shadeName ) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String toDay = dateFormat.format(Calendar.getInstance().getTime());
        /* String sql = "select notification_id from presentation_details where deleted is null and shade_name is not null and shade_name <> '' and shade_name=:shadeName and notification_id <> " + notificationId+
                " and notification_id not in (select id from notifications where dav_expiring_date <= '" + toDay + "' )"; */
        String sql = "select notification_id from presentation_details where deleted is null and shade_name is not null and shade_name <> '' and shade_name=:shadeName and notification_id <> " + notificationId;
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("shadeName",shadeName);
        return this.dao.selectLongID(sql,map);
    }

    protected void validateEntity(PresentationDetail entity) {
        if(entity.manufacturerId != null) {
            Factory manufacturer = factoryService.get(entity.manufacturerId);
            entity.manufacturerAddress = manufacturer.address;
            entity.manufacturerCountryId = manufacturer.countryId;
            entity.manufacturerCountryName = manufacturer.countryName;
            entity.manufacturerFaxNumber = manufacturer.faxNumber;
            entity.manufacturerName = manufacturer.name;
            entity.manufacturerPhoneNumber = manufacturer.phoneNumber;
        }
        if(entity.assemblerId != null) {
            Factory assembler = factoryService.get(entity.assemblerId);
            entity.assemblerAddress = assembler.address;
            entity.assemblerCountryId = assembler.countryId;
            entity.assemblerCountryName = assembler.countryName;
            entity.assemblerFaxNumber = assembler.faxNumber;
            entity.assemblerName = assembler.name;
            entity.assemblerPhoneNumber = assembler.phoneNumber;
        }
        if(entity.exporterId != null) {
            ExporterFactory exporter = exporterFactoryService.get(entity.exporterId);
            entity.exporterAddress = exporter.address;
            entity.exporterCountryId = exporter.countryId;
            entity.exporterCountryName = exporter.countryName;
            entity.exporterFaxNumber = exporter.faxNumber;
            entity.exporterName = exporter.name;
            entity.exporterPhoneNumber = exporter.phoneNumber;
        }
        if(entity.formulaNumber != null && entity.filCode != null) {
            entity.ingredients = ingredientService.findByFLAandFIL(entity.formulaNumber, entity.filCode);
        }
    }
}
