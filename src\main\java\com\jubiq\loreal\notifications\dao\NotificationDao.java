package com.jubiq.loreal.notifications.dao;

import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.notifications.models.Notification;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.skife.jdbi.v2.Query;
import org.skife.jdbi.v2.util.IntegerMapper;

import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/19/16.
 */
public class NotificationDao extends JubiqDao<Long,Notification> {
    public NotificationDao(DBI dbi) {
        super(dbi,Notification.class,Long.class);
    }

    @Override
    public List<Notification> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        order = (order == null || order.length() == 0) ? "notifications.created desc" : order;
        if(order.startsWith("created")) {
            order = "notifications." + order;
        }
        query = generateCustomQuery(query);
        StringBuilder sb = new StringBuilder("select distinct notifications.* from ")
                .append("notifications right join presentation_details")
                .append(" on notifications.id=presentation_details.notification_id")
                .append(" where notifications.deleted is NULL AND presentation_details.deleted is NULL")
                .append((query == null || query.length() == 0) ? "" : " AND " + query)
                .append(" order by ").append(order)
                .append(" limit ").append(limit)
                .append(" offset ").append(offset);

        String sql = sb.toString();
        Handle handle = dbi.open();
        try {
            Query<Notification> dbiQuery = handle.createQuery(sql).map(mapper);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    @Override
    public int count(String whereExp) throws JubiqPersistenceException {
        whereExp = generateCustomQuery(whereExp);
        Handle handle = dbi.open();
        try {
            StringBuilder sb = new StringBuilder("select count(DISTINCT notifications.id) from ")
                    .append("notifications right join presentation_details")
                    .append(" on notifications.id=presentation_details.notification_id")
                    .append(" where notifications.deleted is NULL and presentation_details.deleted is NULL");
            if(whereExp != null && whereExp.length() > 0) {
                sb.append(" AND ").append("(").append(whereExp).append(")");
            }

            Query<Map<String,Object>> query = handle.createQuery(sb.toString());
            return query.map(IntegerMapper.FIRST).first();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }
    private String generateCustomQuery(String query){
        // truong hop individual_product_name dang lay theo truong shade_name -> khong tao them cot moi nen dung cach nay
        if (query.contains("individual_product_name")){
            query= query.replace("individual_product_name", "");
            query = query + " and (presentation_type  = 'COMBINATION') ";
        }
        if (query.contains("individual_shade_name")){
            query= query.replace("individual_shade_name", "");
            query = query + " and (presentation_type  != 'COMBINATION') ";
        }
//        query+=" and (percentage  <= 90.0429) and (percentage  >= 89.5749) and (ingredients like '%WATER%')";
        // (percentage  > 90.01) AND (ingredients  like '%water%')
        // bug nếu để percentage đầu tiên thì không có and
        StringBuilder queryFull = new StringBuilder("");
        String querySplit[] = query.split("\\)");
        StringBuilder conditionString = new StringBuilder(" ( EXISTS ( SELECT 1 FROM presentation_details pdt, " +
                " json_table (REPLACE (REPLACE (REPLACE (REPLACE (REPLACE (REPLACE (REPLACE ( REPLACE ( REPLACE ( REPLACE ( presentation_details.ingredients, ',0', '.0' ), ',1', '.1' ), ',2', '.2' ), ',3', '.3' ),',4','.4'),',5','.5'),',6','.6'),',7','.7' ),',8','.8' ),',9','.9' ),'$[*]' " +
                " COLUMNS ( fullName VARCHAR ( 255 ) PATH '$.fullName', percentage FLOAT ( 10, 4 ) PATH '$.percentage' )) AS jt where  pdt.id = presentation_details.id ")
                ;
        if(query.contains("percentage") || query.contains("ingredients")){

            for(int i = 0; i< querySplit.length ; i ++){
                if(querySplit[i].contains("percentage")){
                    if(i == 0 &&  (! (querySplit[i].contains("AND") && querySplit[i].contains("OR")))){
                        conditionString.append(" AND ");
                    }
                    conditionString.append(querySplit[i]).append(")");
                    query = query.replace(querySplit[i]+")", "");
                }
                else if(querySplit[i].contains("ingredients")){
                    String fullName = querySplit[i].replace("ingredients","fullName");
                    if(i == 0 &&  (! (querySplit[i].contains("AND") && querySplit[i].contains("OR")))){
                        conditionString.append(" AND ");
                    }
                    conditionString.append(fullName).append(")");
                    query = query.replace(querySplit[i]+")", "");
                }
            }
            conditionString.append("))");
            if(querySplit[0].contains("percentage") || query.isEmpty()){
                queryFull.append(conditionString).append(query);
            }
            else{
                queryFull.append(query).append(" AND ").append(conditionString);
            }
        }


        LOGGER.info("queryFull: {}" +queryFull);
        LOGGER.info("query: {}"+query);
        return queryFull.toString().length() > 0 ? queryFull.toString() : query ;
    }

    @Override
    protected void generateGetSql() {
        getSql = "SELECT * from " + tableName + " WHERE id = :id";
    }
}
