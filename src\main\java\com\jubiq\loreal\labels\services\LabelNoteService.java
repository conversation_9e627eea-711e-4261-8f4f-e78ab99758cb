package com.jubiq.loreal.labels.services;

import com.jubiq.loreal.LorealConstants;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.labels.dao.LabelDao;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.labels.models.LabelNote;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.services.AlertService;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Singleton
public class LabelNoteService extends JubiqService<Long,LabelNote> {
    private LabelDao labelDao;
    private AlertService alertService;
    @Inject
    public LabelNoteService(DaoFactory daoFactory, AlertService alertService) {
        this.dao = daoFactory.createDao(LabelNote.class,Long.class);
        this.labelDao = daoFactory.getLabelDao();
        this.alertService = alertService;
    }

    public List<LabelNote> findByLabel(Long labelId) {
        String sql = "select * from label_notes where label_id=:labelId and deleted is null order by created desc";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("labelId",labelId);
        return this.dao.search(sql,map);
    }

    @Override
    public LabelNote create(LabelNote entity, JubiqSession session) throws JubiqPersistenceException {
        Label label = labelDao.get(entity.labelId);
        entity.creatorFullName = session.fullName;
        entity.creatorId = session.userId;

        LabelNote note = super.create(entity,session);

        Alert alert = new Alert(session.userId,session.fullName,"note-label");
        alert.params.put("labelId",entity.labelId.toString());
        alert.params.put("productName",label.productName);
        alertService.createAlert(alert, LorealConstants.SCI_GROUP_ID,null);
        alertService.createAlert(alert, LorealConstants.LOGISTICS_GROUP_ID,null);
        alertService.createAlert(alert, null,label.followerId);
        return note;
    }
}

