package com.jubiq.loreal.common.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.jubiq.loreal.FileConfiguration;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqFile;
import com.jubiq.loreal.common.persistence.DaoFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * Created by vietnq on 12/26/15.
 */
@Singleton
public class FileService extends JubiqService<Long,JubiqFile> {
    private String fileDir;
    @Inject
    public FileService(DaoFactory daoFactory,FileConfiguration fileConfiguration) {
        this.dao = daoFactory.createDao(JubiqFile.class, Long.class);
        this.fileDir = fileConfiguration.fileDir;
    }

    public JubiqFile writeToFile(InputStream uploadedInputStream, String fileName) throws JubiqPersistenceException, IOException, EntityNotFoundException {
        JubiqFile file = new JubiqFile();
        file.name = fileName;
        file = create(file, null);
        file.uri = this.fileDir + file.id + fileName;

        int read;
        final int BUFFER_LENGTH = 1024;
        final byte[] buffer = new byte[BUFFER_LENGTH];
        try {
            OutputStream out = new FileOutputStream(new File(file.uri));
            while ((read = uploadedInputStream.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            out.flush();
            out.close();

        } catch(IOException e) {
            delete(file.id, null);
            throw e;
        }
        update(file.id, file, null);
        return file;
    }
}
