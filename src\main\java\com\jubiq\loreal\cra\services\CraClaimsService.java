package com.jubiq.loreal.cra.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.cra.models.CraClaims;
import com.jubiq.loreal.notifications.models.History;

import java.util.List;

/**
 * Created by AI Assistant on 7/11/25.
 * Service for CRA Claims management
 */
@Singleton
public class CraClaimsService extends JubiqService<Long, CraClaims> {
    
    private HistoryService historyService;
    
    @Inject
    public CraClaimsService(DaoFactory daoFactory, HistoryService historyService) {
        this.dao = daoFactory.createDao(CraClaims.class, Long.class);
        this.historyService = historyService;
    }
    
    @Override
    public CraClaims create(CraClaims entity, JubiqSession session) throws JubiqPersistenceException {
        entity.creatorId = session != null ? session.userId : 1000L;
        beforeCreate(entity);
        CraClaims claims = this.dao.create(entity);
        
        if (session != null) {
            historyService.createHistory(new History(entity.id, "CRA_CLAIMS", "create", 
                session.userId, session.fullName, null, null, null));
        }
        
        return claims;
    }
    
    @Override
    public void update(Long id, CraClaims entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        beforeUpdate(id, entity);
        this.dao.update(entity);

        if (session != null) {
            historyService.createHistory(new History(id, "CRA_CLAIMS", "update",
                session.userId, session.fullName, null, null, null));
        }

        afterUpdate(id, entity);
    }
    
    /**
     * Get claims by CRA request ID
     */
    public List<CraClaims> getClaimsByRequestId(Long craId) throws JubiqPersistenceException {
        String query = "cra_id = " + craId;
        return this.dao.search(query, 1000, 0, "created desc");
    }

    /**
     * Get claims by approval status
     */
    public List<CraClaims> getClaimsByApprovalStatus(String approvalStatus, int limit, int offset) throws JubiqPersistenceException {
        String query = "approval_status = '" + approvalStatus + "'";
        return this.dao.search(query, limit, offset, "created desc");
    }

    /**
     * Get claims by marketing acceptance status
     */
    public List<CraClaims> getClaimsByMktStatus(String mktStatus, int limit, int offset) throws JubiqPersistenceException {
        String query = "mkt_accepted_status = '" + mktStatus + "'";
        return this.dao.search(query, limit, offset, "created desc");
    }
}
