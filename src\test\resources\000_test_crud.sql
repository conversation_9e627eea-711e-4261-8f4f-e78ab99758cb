--liquibase formatted sql

--changeset vietnq:1
CREATE TABLE test_long_ids (
  id INTEGER NOT NULL,
  created BIGINT,
  updated BIGINT,
  full_name VARCHAR(255),
  address TEXT,
  deleted BIGINT,
  skills TEXT,
  relations TEXT,
  job_type VARCHAR(255),
  birth_day DATE,
  active BOOLEAN,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE test_string_ids (
  id VARCHAR(255) NOT NULL,
  created BIGINT,
  updated BIGINT,
  full_name VARCHAR(255),
  address TEXT,
  deleted BIGINT,
  skills TEXT,
  relations TEXT,
  job_type VARCHAR(255),
  birth_day DATE,
  active BOOLEAN,
  creator_id INTEGER,
  PRIMARY KEY (id)
);
--rollback;