package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.notifications.models.History;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/21/16.
 */
@Path("/api/histories")
@Api("Histories")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class HistoryEndpoint extends JubiqEndpoint<Long,History> {
    private HistoryService historyService;
    @Inject
    public HistoryEndpoint(HistoryService historyService) {
        this.service = this.historyService = historyService;
    }

    @GET
    @Path("/query")
    @ApiOperation("Query history by notification")
    public Response query(@Auth JubiqSession session, @QueryParam("notificationId") Long notificationId) {
        List<History> histories = historyService.findByNotificationId(notificationId);
        return Response.ok(histories).build();
    }

    @GET
    @Path("/query-label")
    @ApiOperation("Query history by label")
    public Response queryLabel(@Auth JubiqSession session, @QueryParam("labelId") Long labelId) {
        List<History> histories = historyService.findByLabelId(labelId);
        return Response.ok(histories).build();
    }

    @GET
    @Path("/query-advertising")
    @ApiOperation("Query history by advertising")
    public Response queryAdvertising(@Auth JubiqSession session, @QueryParam("advertisingId") Long advertisingId) {
        List<History> histories = historyService.findByAdvertisingId(advertisingId);
        return Response.ok(histories).build();
    }

    @GET
    @Path("/query-cra")
    @ApiOperation("Query history by CRA")
    public Response queryCra(@Auth JubiqSession session, @QueryParam("craId") Long craId) {
        List<History> histories = historyService.findByCraId(craId);
        return Response.ok(histories).build();
    }
}
