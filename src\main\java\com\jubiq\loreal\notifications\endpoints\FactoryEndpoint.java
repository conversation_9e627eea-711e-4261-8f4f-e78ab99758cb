package com.jubiq.loreal.notifications.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Factory;
import com.jubiq.loreal.notifications.services.FactoryService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by vietnq on 1/5/16.
 */
@Path("/api/factories")
@Api("Factories")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class FactoryEndpoint extends JubiqEndpoint<Long,Factory>{
    private FactoryService factoryService;
    @Inject
    public FactoryEndpoint(FactoryService factoryService) {
        this.service = this.factoryService = factoryService;
    }

    @GET
    @Path("/country-factories")
    @ApiOperation("Get factories by country")
    public Response factoriesByCountry(@Auth JubiqSession session, @QueryParam("countryId") Long countryId) {
        List<Factory> factories = this.factoryService.factoriesOfCountry(countryId);
        return Response.ok(factories).build();
    }
}
