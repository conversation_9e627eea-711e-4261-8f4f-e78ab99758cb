--liquibase formatted sql
--changeset vietnq:1009

INSERT INTO permissions(id,description) VALUES ('API:ALERT:FIND','Allow to find alert');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (77,2002,'API:ALERT:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (78,3000,'API:ALERT:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (79,2002,'API:BRAND:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (80,3000,'API:BRAND:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (81,2002,'API:USERPREFERENCE:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (82,3000,'API:USERPREFERENCE:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (83,2002,'API:HISTORY:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (84,3000,'API:HISTORY:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (85,2002,'API:HISTORY:READ');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (86,3000,'API:HISTORY:READ');

--rollback