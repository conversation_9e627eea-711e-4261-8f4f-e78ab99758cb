package com.jubiq.loreal.umgr.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.*;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.notifications.models.Filter;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.models.UserPreference;
import com.jubiq.loreal.umgr.services.UserPreferenceService;
import com.jubiq.loreal.umgr.services.UserService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
/**
 * Created by vietnq on 11/18/15.
 */
@Path("/api/users")
@Api("users")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class UserEndpoint extends JubiqEndpoint<Long,User> {

    private static Logger LOGGER = LoggerFactory.getLogger(UserEndpoint.class);

    private UserService userService;

    private UserPreferenceService userPreferenceService;

    private NotificationService notificationService;

    @Inject
    public UserEndpoint(UserService userService,
                        NotificationService notificationService,
                        UserPreferenceService userPreferenceService) {
        super();
        this.service = this.userService = userService;
        this.userPreferenceService = userPreferenceService;
        this.notificationService = notificationService;
    }

    @POST
    @Path("/forgot-password")
    @ApiOperation("send forgot password email")
    public Response doForgotPassword(@ApiParam @FormParam("email") String email) throws UnauthorizedException, JubiqPersistenceException, EntityNotFoundException, InvalidRequestException {
        if(email == null) {
            throw new InvalidRequestException("Email is required");
        }
        User user = userService.findByEmail(email);
        userService.sendForgotPasswordEmail(user.id);
        return Response.ok().build();
    }

    @POST
    @Path("/{userId}/change-forgotten-password")
    @ApiOperation("Change password using forgot password flow")
    public Response doChangeForgottenPassword(@PathParam("userId") Long userId,
                                              @DefaultValue("") @FormParam("password") String newPassword,
                                              @DefaultValue("") @FormParam("token") String token) throws JubiqPersistenceException, InvalidForgotPasswordTokenException, ForgotPasswordTokenExpiredException, InvalidRequestException, EntityNotFoundException, UnauthorizedException {
        userService.changeForgottenPassword(userId, newPassword, token);
        return Response.ok().build();
    }

    @POST
    @Path("/{userId}/set-password")
    @ApiOperation("Set new password for user")
    public Response doSetPassword(@Auth JubiqSession session, @PathParam("userId") Long userId,
                                              @DefaultValue("") @FormParam("password") String newPassword) throws JubiqPersistenceException, InvalidForgotPasswordTokenException, ForgotPasswordTokenExpiredException, InvalidRequestException, EntityNotFoundException, InvalidOwnerResourceException {
        try {
            authorize(session,"API:USER:PASSWORD");
        } catch (UnauthorizedException e) {
            checkOwner(session, userId);
        }
        userService.setPassword(userId,newPassword);
        return Response.ok().build();
    }

    @POST
    @Path("/{userId}/change-password")
    @ApiOperation("Change password for user")
    public Response doChangePassword(@Auth JubiqSession session, @PathParam("userId") Long userId,
                                              @DefaultValue("") @FormParam("currentPassword") String currentPassword,
                                              @DefaultValue("") @FormParam("newPassword") String newPassword) throws JubiqPersistenceException, InvalidForgotPasswordTokenException, ForgotPasswordTokenExpiredException, InvalidRequestException, EntityNotFoundException, InvalidOwnerResourceException {
        try {
            authorize(session,"API:USER:PASSWORD");
        } catch (UnauthorizedException e) {
            checkOwner(session, userId);
        }
        userService.changePassword(userId,currentPassword,newPassword);
        return Response.ok().build();
    }

    @GET
    @Path("/{userId}/filters")
    @ApiOperation("Get filters of user")
    public Response getUserFilters(@Auth JubiqSession session, @PathParam("userId") Long userId,@QueryParam("customQuery") String customQuery) {
        checkOwner(session, userId);
        List<Filter> filters = filterService.userFilters(userId);
        for(Filter filter : filters) {
            String toCountSql = filter.query;
            if(customQuery != null && customQuery.length() > 0) {
             toCountSql += " AND (" + customQuery + ")";
            }
            if(filter.scope.equals("GLOBAL")) {
              filter.numberOfItems = notificationService.countNotJoin(toCountSql);
            }
            else{
              filter.numberOfItems = notificationService.count(toCountSql);
            }
        }
        return Response.ok(filters).build();
    }

    @GET
    @Path("/{userId}/preferences")
    @ApiOperation("Get user preference")
    public Response getUserPreferences(@Auth JubiqSession session, @PathParam("userId") Long userId) {
        checkOwner(session, userId);
        UserPreference userPreference = userPreferenceService.userPreference(userId);
        return Response.ok(userPreference).build();
    }

    @POST
    @Path("/users-by-groups")
    @ApiOperation("Get users by a list of group names")
    public Response getUsersByListGroups(@Auth JubiqSession session, @ApiParam List<String> groupNames) {

        List<User> users = userService.usersOfGroups(groupNames);
        return Response.ok(users).build();
    }

    @GET
    @Path("/find-not-login")
    public Response findNotLogin() {
        List<User> users = userService.getAll();
        return Response.ok(users).build();
    }

}
