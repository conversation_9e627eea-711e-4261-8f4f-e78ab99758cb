package com.jubiq.loreal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jubiq.loreal.common.service.MailConfiguration;
import io.dropwizard.Configuration;
import io.dropwizard.db.DataSourceFactory;
import io.federecio.dropwizard.swagger.SwaggerBundleConfiguration;

import javax.validation.Valid;

/**
 * Created by vietnq2 on 11/12/15.
 */
public class LorealConfiguration extends Configuration {
    @Valid
    public DataSourceFactory database;

    @JsonProperty("swagger")
    public SwaggerBundleConfiguration swaggerBundleConfiguration;

    @JsonProperty("mailConfiguration")
    public MailConfiguration mailConfiguration;

    @JsonProperty("fileConfiguration")
    public FileConfiguration fileConfiguration;

    @JsonProperty("serverUrl")
    public String serverUrl;
}
