--liquibase formatted sql
--changeset thanhlx:2007

ALTER TABLE `alerts` CHANGE `action` `action` VARCHAR(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

ALTER TABLE `advertisings`
  DROP `channels`,
  DROP `channels_desc`,
  DROP `files`;

ALTER TABLE `advertisings` CH<PERSON>GE `id` `id` INT(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `advertisings` ADD `keyvisuals` TEXT NULL AFTER `validated`, ADD `advertising_media_selection` TEXT NULL AFTER `keyvisuals`, ADD `request_ids` VARCHAR(1024) NULL AFTER `advertising_media_selection`, ADD `validated_time` DATE NULL AFTER `request_ids`, ADD `license_requesting_date` DATE NULL AFTER `validated_time`, ADD `license_files` TEXT NULL AFTER `license_requesting_date`, ADD `license_number` TEXT NULL AFTER `license_files`, ADD `license_receiving_date` DATE NULL AFTER `license_number`;

INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:CREATE','Allow to create advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:FIND','Allow to search for advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:READ','Allow to view advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:DELETE','Allow to delete advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:UPDATE','Allow to update advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:SUBMIT','Allow to submit advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:VALIDATE','Allow to validate advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISING:REJECT','Allow to reject advertising',1447653450436,NULL,NULL);

INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISINGXLSTEMPLATE:CREATE','Allow to create xls template for advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISINGXLSTEMPLATE:FIND','Allow to search xls template for advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISINGXLSTEMPLATE:READ','Allow to view xls template for advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISINGXLSTEMPLATE:DELETE','Allow to delete xls template for advertising',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:ADVERTISINGXLSTEMPLATE:UPDATE','Allow to update xls template for advertising',1447653450436,NULL,NULL);

ALTER TABLE `user_preferences` ADD `selected_advertising_fields` TEXT NULL AFTER `selected_advertising_fields`;

CREATE TABLE `advertising_xls_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `field_names` text DEFAULT NULL,
  `created` bigint(20) DEFAULT NULL,
  `updated` bigint(20) DEFAULT NULL,
  `deleted` bigint(20) DEFAULT NULL,
  `creator_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


ALTER TABLE `advertising_xls_templates`
  ADD PRIMARY KEY (`id`);
COMMIT;

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('validate-advertising.alert.email.template', 'Advertising ${advertisingId} is validated by ${sourceFullName}.
<br>
Please click <a href="http://***********/advertising/update/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('submit-advertising.alert.email.template', 'Advertising ${advertisingId} is submitted by ${sourceFullName}. <br> Please click <a href="http://***********/advertising/process/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('reject-advertising.alert.email.template', 'Advertising ${advertisingId} is rejected by ${sourceFullName}. <br> Please click <a href="http://***********/advertising/update/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('delete-advertising.alert.email.template', 'Advertising ${advertisingId} is deleted by ${sourceFullName}.', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('submit-license-advertising.alert.email.template', 'License of dvertising ${advertisingId} is submitted by ${sourceFullName}. <br> Please click <a href="http://***********/advertising/update/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('receive-license-advertising.alert.email.template', 'License of advertising ${advertisingId} is received by ${sourceFullName}. <br> Please click <a href="http://***********/advertising/update/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

--rollback;