package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq on 12/25/15.
 */
public class ForgotPasswordTokenExpiredException extends JubiqException {
    public ForgotPasswordTokenExpiredException(Throwable e) {
        super(e);
    }

    public ForgotPasswordTokenExpiredException(String message) {
        super(message);
    }

    public ForgotPasswordTokenExpiredException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.FORGOT_PWD_TOKEN_EXPIRED.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.FORGOT_PWD_TOKEN_EXPIRED.name();
    }
}
