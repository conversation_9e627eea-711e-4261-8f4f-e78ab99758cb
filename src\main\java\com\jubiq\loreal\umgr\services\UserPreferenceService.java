package com.jubiq.loreal.umgr.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.LorealConstants;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.umgr.models.UserPreference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 12/15/15.
 */
@Singleton
public class UserPreferenceService extends JubiqService<Long, UserPreference> {
    @Inject
    public UserPreferenceService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(UserPreference.class, Long.class);
    }

    public UserPreference userPreference(Long userId) throws JubiqPersistenceException {
        String query = "select * from user_preferences where user_id=:userId";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("userId",userId);
        List<UserPreference> userPreferences = this.dao.search(query,map);
        if(userPreferences != null && userPreferences.size() > 0) {
            UserPreference preference = userPreferences.get(0);
            if(preference.selectedFields == null || preference.selectedFields.size() == 0) {
                preference.selectedFields = LorealConstants.DEFAULT_SELECTED_FIELDS;
            }
            if(preference.selectedLabelFields == null || preference.selectedLabelFields.size() == 0) {
                preference.selectedLabelFields = LorealConstants.DEFAULT_SELECTED_LABEL_FIELDS;
            }
            if(preference.selectedAdvertisingFields == null || preference.selectedAdvertisingFields.size() == 0) {
                preference.selectedAdvertisingFields = LorealConstants.DEFAULT_SELECTED_ADVERTISING_FIELDS;
            }
            if(preference.selectedCraFields == null || preference.selectedCraFields.isEmpty()) {
                preference.selectedCraFields = LorealConstants.DEFAULT_SELECTED_CRA_FIELDS;
            }
            return preference;
        } else {
            return null;
        }
    }
}
