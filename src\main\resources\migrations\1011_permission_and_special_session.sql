--liquibase formatted sql
--changeset vietnq:1011

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (94,3000,'API:GROUP:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (95,3000,'API:GROUP:READ');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (96,3000,'API:PRODUCTTYPE:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (97,3000,'API:PRODUCTTYPE:READ');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (98,3000,'API:BRAND:READ');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (99,3000,'API:COUNTRY:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (100,3000,'API:COUNTRY:READ');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (101,2002,'API:USER:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (102,2002,'API:USER:READ');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (103,3000,'API:USER:FIND');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (104,3000,'API:USER:READ');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (105,2002,'API:BRAND:CREATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (106,2002,'API:BRAND:UPDATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (107,2002,'API:BRAND:DELETE');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (108,2002,'API:COUNTRY:CREATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (109,2002,'API:COUNTRY:UPDATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (110,2002,'API:COUNTRY:DELETE');

INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (111,2002,'API:PRODUCTTYPE:CREATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (112,2002,'API:PRODUCTTYPE:UPDATE');
INSERT INTO groups_permissions (id,group_id,permission_id) VALUES (113,2002,'API:PRODUCTTYPE:DELETE');

--rollback