--liquibase formatted sql

--changeset vietnq:2
CREATE TABLE brands (
  id INTEGER NOT NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE product_types (
  id INTEGER NOT NULL,
  description MEDIUMTEXT,
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

--used for both manufacturers and assemblers company
CREATE TABLE factories (
  id INTEGER NOT NULL,
  name VA<PERSON>HAR(255),
  address MEDIUMTEXT,
  phone_number VARCHAR(255),
  fax_number VARCHAR(255),
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE countries (
  id INTEGER NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE notifications (
  id INTEGER NOT NULL,
  brand_name <PERSON><PERSON><PERSON><PERSON>(255),
  product_name VARCHAR(255),
  product_range VARCHAR(255),
  product_type_id INTEGER,
  aw_uri MEDIUMTEXT,
  launch_time DATE,
  shipment_request_time DATE,
  presentation_type VARCHAR(36),
  product_code VARCHAR(32),
  formula_number VARCHAR(32),
  fil_code VARCHAR(32),
  intended_use MEDIUMTEXT,
  origin_country_id INTEGER,
  manufacturer_id INTEGER,
  assembler_id INTEGER,
  packaging_sizes TEXT, --packing list in json string
  shades TEXT, --shades list in json string (for range of colors and palette)
  validated_time DATE,
  checking_time DATE,
  cfs_requesting_date DATE,
  cfs_available BOOLEAN,
  cfs_receiving_date DATE,
  inci_requesting_date DATE,
  inci_available BOOLEAN,
  inci_receiving_date DATE,
  dav_notification_number VARCHAR(32),
  dav_receiving_date DATE,
  dav_expiring_date DATE,
  dav_notification_uri MEDIUMTEXT,
  ingredients TEXT, --ingredients in json string
  assignee_id INTEGER,
  follower_id INTEGER,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  outlook_file_uri VARCHAR(255),
  status VARCHAR(36),
  PRIMARY KEY (id)
);

--Table for individual product for Notification of type Combination
CREATE TABLE individual_products (
  id INTEGER NOT NULL,
  notification_id BIGINT NOT NULL,
  product_code VARCHAR(32),
  composite_number VARCHAR(32),
  formula_number VARCHAR(32),
  fil_code VARCHAR(32),
  packaging_size VARCHAR(255),
  intended_use MEDIUMTEXT,
  country_id INTEGER,
  manufacturer_id INTEGER,
  assembler_id INTEGER,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);


CREATE TABLE user_alerts (
  id INTEGER NOT NULL,
  content TEXT,
  user_id INTEGER,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);
--rollback ...;