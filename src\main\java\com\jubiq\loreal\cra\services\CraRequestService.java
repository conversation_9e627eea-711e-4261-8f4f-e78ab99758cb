package com.jubiq.loreal.cra.services;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.advertisings.models.Advertising;
import com.jubiq.loreal.advertisings.models.KeyvisualMessage;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.FileService;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.cra.models.CRAStatus;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.models.History;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.notifications.services.ConfigurationService;
import com.jubiq.loreal.notifications.services.ExportService;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.common.service.MailService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.GroupService;
import com.jubiq.loreal.umgr.services.UserService;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.List;

/**
 * Created by AI Assistant on 7/9/25.
 * Service for CRA Request management
 */
@Singleton
public class CraRequestService extends JubiqService<Long, CraRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CraRequestService.class);
    private SimpleDateFormat dateFormater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private UserService userService;
    private GroupService groupService;
    private HistoryService historyService;
    private AlertService alertService;
    private FileService fileService;
    private ObjectMapper jsonMapper;
    private ExportService exportService;
    private NotificationService notificationService;
    private MailService mailService;
    private ConfigurationService configurationService;

    @Inject
    public CraRequestService(DaoFactory daoFactory,
                              UserService userService,
                              GroupService groupService,
                              HistoryService historyService,
                              AlertService alertService,
                              FileService fileService,
                              ExportService exportService,
                              MailService mailService,
                              ConfigurationService configurationService) {
        this.dao = daoFactory.createDao(CraRequest.class,Long.class);
        this.userService = userService;
        this.groupService = groupService;
        this.historyService = historyService;
        this.alertService = alertService;
        this.fileService = fileService;
        this.exportService = exportService;
        this.notificationService=  notificationService;
        this.mailService = mailService;
        this.configurationService = configurationService;
        jsonMapper = new ObjectMapper();
        jsonMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Override
    public CraRequest create(CraRequest entity, JubiqSession session) throws JubiqPersistenceException {

        if (entity.status == null) {
            entity.status = CRAStatus.WAIT_FOR_MKT_SUBMISSION;
        }
        entity.creatorId = entity.followerId = session.userId;

        // Add sequential ID generation
        if(entity.id == null) {
            Long currentId = currentIncrementValue();
            entity.id = currentId;
        }

        beforeCreate(entity);
        CraRequest request = this.dao.create(entity);
        if(session != null) {
            historyService.createHistory(new History(entity.id,"CRA","create",session.userId,session.fullName,null,null,null));
        }
        return request;
    }

    @Override
    public void update(Long id, CraRequest entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        CraRequest existing = get(id);

        // Validate the entity before update
//        beforeUpdate(id, entity);

        // Check for new messages before updating
        checkAndSendNewMessageNotifications(existing, entity, session);

        // Update the entity
        this.dao.update(entity);
        CraRequest updated = get(id);

        // Create JSON strings for history tracking
        String oldValue = null;
        String newValue = null;
        try {
            oldValue = jsonMapper.writeValueAsString(existing);
            newValue = jsonMapper.writeValueAsString(updated);
        } catch (JsonProcessingException e) {
            LOGGER.info("Cannot parse JSON string for CRA request history");
        }

        // Create history record
        historyService.createHistory(new History(id, "CRA", "update", session.userId, session.fullName, oldValue, newValue, null));

        // Check for field differences and create alert if needed
        String differFields = entity.compareFields(existing).stream().collect(Collectors.joining(", "));
        if(!differFields.isEmpty()) {
            Alert alert = new Alert(session.userId, session.fullName, "update-cra-request");
            alert.params.put("craRequestId", id.toString());
            alert.params.put("differFields", differFields);
            alertService.createAlert(alert, null, updated.followerId);
        }
    }

    /**
     * Get the current AUTO_INCREMENT value for sequential ID generation
     */
    private Long currentIncrementValue() {
        String sql = "select auto_increment from information_schema.TABLES where TABLE_SCHEMA='loreal' and TABLE_NAME='cra_requests'";
        return this.dao.selectLong(sql);
    }

    @Override
    protected void validateEntity(CraRequest entity) {
//        super.validateEntity(entity);

        if (entity.followerId != null) {
            User follower = userService.getDao().get(entity.followerId);
            entity.followerEmail = follower.email;
            entity.followerFullName = follower.fullName;
        }
        if (entity.assigneeId != null) {
            User assignee = userService.getDao().get(entity.assigneeId);
            entity.assigneeEmail = assignee.email;
            entity.assigneeFullName = assignee.fullName;
        }
        if (entity.creatorId != null) {
            User creator = userService.getDao().get(entity.creatorId);
            entity.creatorEmail = creator.email;
            entity.creatorFullName = creator.fullName;
        }
    }

    public File exportXls(String sheetName, List<CraRequest> craRequests, List<String> fieldNames, Long callerId) throws IOException {
        File file = exportService.exportCraRequestsToXls(sheetName, craRequests, fieldNames);
        return file;
    }

    public void submit(Long id, JubiqSession session) throws JubiqPersistenceException {
        CraRequest craRequest = this.dao.get(id);

        // Check if user is the follower (owner) of the request
//        if (!session.userId.equals(craRequest.followerId)) {
//            throw new UnauthorizedException("You are not follower of the CRA request");
//        }

        // Check if status is valid for submission
        if (craRequest.status != CRAStatus.WAIT_FOR_MKT_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_MKT_SUBMISSION");
        }

        // Set submission date
        craRequest.dateOfSubmitting = new Date();

        // Change status to next step in workflow
        craRequest.status = CRAStatus.WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN;

        // Update the request
        this.dao.update(craRequest);

        // Create history record
        historyService.createHistory(new History(id, "CRA", "submit", session.userId, session.fullName, null, null, null));

        // Create alert for STRD director or assignee
        Alert alert = new Alert(session.userId, session.fullName, "submit-cra-request");
        alert.params.put("craRequestId", id.toString());

        // For now, alert to assignee if exists, otherwise we'll need to define STRD_DIRECTOR_GROUP_ID
        if (craRequest.assigneeId != null) {
            alertService.createAlert(alert, null, craRequest.assigneeId);
        }
        // TODO: Define STRD_DIRECTOR_GROUP_ID constant and uncomment below
        // else {
        //     alertService.createAlert(alert, STRD_DIRECTOR_GROUP_ID, null);
        // }
    }

    public void assign(Long id, Long assigneeId, JubiqSession session) throws JubiqPersistenceException {
        CraRequest craRequest = this.dao.get(id);

        // Check if status is valid for assignment
        if (craRequest.assigneeId == null && !CRAStatus.WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN.equals(craRequest.status)) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_STRD_DIRECTOR_TO_ASSIGN");
        }

        Boolean reAssign = craRequest.assigneeId != null;

        try {
            User user = this.userService.get(assigneeId);

            craRequest.assigneeId = user.id;
            craRequest.assigneeEmail = user.email;
            craRequest.assigneeFullName = user.fullName;

            // Change status to next step if it's first assignment
            if (!reAssign) {
                craRequest.status = CRAStatus.WAIT_FOR_STRAD_RISK_ASSESSMENT;
            }

            // Update the request
            this.dao.update(craRequest);

            // Create history record
            historyService.createHistory(new History(id, "CRA", reAssign ? "re-assign" : "assign", session.userId, session.fullName, null, null, null));

            // Create alert for assignee
            Alert alert = new Alert(session.userId, session.fullName, reAssign ? "re-assign-cra-request" : "assign-cra-request");
            alert.params.put("craRequestId", id.toString());
            alert.params.put("expositionDetail", craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request");
            alertService.createAlert(alert, null, craRequest.assigneeId);

        } catch (EntityNotFoundException e) {
            throw new InvalidRequestException("No assignee with id " + assigneeId);
        }
    }

    public void saveRiskAssessment(Long id, JubiqSession session) throws JubiqPersistenceException {
        CraRequest craRequest = this.dao.get(id);

//        // Check if user is the assignee
//        if (!session.userId.equals(craRequest.assigneeId)) {
//            throw new UnauthorizedException("You are not assignee of the CRA request");
//        }

        // Check if status is valid for saving risk assessment
        if (!CRAStatus.WAIT_FOR_STRAD_RISK_ASSESSMENT.equals(craRequest.status)) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_STRD_RISK_ASSESSMENT");
        }

        // Set STRD risk assessment date
        craRequest.dateOfStradRiskAssessment = new Date();

        // Generate quick login hashes for GM and CM
        craRequest.gmLoginHash = UUID.randomUUID().toString();
        craRequest.cmLoginHash = UUID.randomUUID().toString();

        // Change status to next step in workflow
        craRequest.status = CRAStatus.WAIT_FOR_APPROVAL;

        // Update the request
        this.dao.update(craRequest);

        // Create history record
        historyService.createHistory(new History(id, "CRA", "save-risk-assessment", session.userId, session.fullName, null, null, null));

        // Create alert for follower (request owner)
        Alert alert = new Alert(session.userId, session.fullName, "save-risk-assessment-cra-request");
        alert.params.put("craRequestId", id.toString());
        alert.params.put("expositionDetail", craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request");
        alertService.createAlert(alert, null, craRequest.followerId);

        // Send approval emails to GM and CM with quick login links
        sendApprovalEmails(craRequest);

    }

    /**
     * Send approval emails to GM and CM with quick login links
     */
    private void sendApprovalEmails(CraRequest craRequest) {
        try {
            // Get email template for CRA approval
            String gmEmailTemplate = getApprovalEmailTemplate("GM");
            String cmEmailTemplate = getApprovalEmailTemplate("CM");

            // Get GM and CM email addresses (should be configurable)
            String gmEmail = getGmEmail();
            String cmEmail = getCmEmail();

            // Generate approval links with quick login hashes
            String baseUrl = "http://localhost:3005"; // Should be configurable
            String gmApprovalLink = String.format("%s/cra/quick-login.html?craRequestId=%d&loginHash=%s&email=%s",
                                                 baseUrl, craRequest.id, craRequest.gmLoginHash, gmEmail);
            String cmApprovalLink = String.format("%s/cra/quick-login.html?craRequestId=%d&loginHash=%s&email=%s",
                                                 baseUrl, craRequest.id, craRequest.cmLoginHash, cmEmail);

            // Format email content
            String gmEmailContent = String.format(gmEmailTemplate,
                                                 craRequest.id,
                                                 craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request",
                                                 gmApprovalLink);
            String cmEmailContent = String.format(cmEmailTemplate,
                                                 craRequest.id,
                                                 craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request",
                                                 cmApprovalLink);

            // Send emails to GM and CM
            mailService.sendMail(gmEmail, "CRA Approval Required - " + craRequest.id, gmEmailContent);
            mailService.sendMail(cmEmail, "CRA Approval Required - " + craRequest.id, cmEmailContent);

            LOGGER.info("Approval emails sent for CRA request {} to GM and CM", craRequest.id);

        } catch (Exception e) {
            LOGGER.error("Failed to send approval emails for CRA request {}", craRequest.id, e);
        }
    }

    /**
     * Get email template for approval
     */
    private String getApprovalEmailTemplate(String userType) {
        try {
            String templateKey = "cra-approval-" + userType.toLowerCase() + ".email.template";
            return configurationService.get(templateKey).value;
        } catch (Exception e) {
            // Return default template if not found
            return "CRA Request %d (%s) requires your approval.<br>" +
                   "Please click <a href=\"%s\">here</a> to approve or reject this request.<br>" +
                   "This link will automatically log you in as " + userType + ".";
        }
    }

    /**
     * Get GM email address from configuration
     */
    private String getGmEmail() {
        try {
            return configurationService.get("cra.gm.email").value;
        } catch (Exception e) {
            // Return default GM email if not configured
            return "<EMAIL>";
        }
    }

    /**
     * Get CM email address from configuration
     */
    private String getCmEmail() {
        try {
            return configurationService.get("cra.cm.email").value;
        } catch (Exception e) {
            // Return default CM email if not configured
            return "<EMAIL>";
        }
    }

    /**
     * Check for new messages and send email notifications
     */
    private void checkAndSendNewMessageNotifications(CraRequest existing, CraRequest updated, JubiqSession session) {
        try {
            // Check if there are new messages
            if (updated.messages != null && updated.messages.size() > 0) {
                for (KeyvisualMessage message : updated.messages) {
                    // Check if this is a new message (no createdTime means it's new)
                    if (message.createdTime == null || message.createdTime.trim().isEmpty()) {
                        // Mark the message as processed by setting createdTime
                        message.createdTime = dateFormater.format(new Date());

                        // Send email notification based on sender role
                        sendMessageNotificationEmail(updated, message, session);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Failed to send message notification emails for CRA request {}", updated.id, e);
        }
    }

    /**
     * Send email notification for new message based on sender role
     */
    private void sendMessageNotificationEmail(CraRequest craRequest, KeyvisualMessage message, JubiqSession session) {
        try {
            String recipientEmail = null;
            String senderName = null;
            String subject = null;

            // Determine sender and recipient based on user role
            if (session.userId.equals(craRequest.followerId)) {
                // Logged-in user is the follower (MKT)
                recipientEmail = craRequest.assigneeEmail;
                senderName = "MKT: " + craRequest.followerFullName;
                subject = "New message from MKT: " + craRequest.followerFullName;
            } else if (isSciRelatedRole(session)) {
                // Logged-in user has SCI-related roles
                recipientEmail = craRequest.followerEmail;
                senderName = session.fullName;
                subject = "New message from " + session.fullName;
            }

            // Send email if recipient is determined and valid
            if (recipientEmail != null && !recipientEmail.trim().isEmpty()) {
                String emailBody = buildMessageEmailBody(craRequest, message, senderName);
                mailService.sendMail(recipientEmail, subject, emailBody);

                LOGGER.info("Message notification email sent to {} for CRA request {} from {}",
                           recipientEmail, craRequest.id, senderName);
            } else {
                LOGGER.warn("No valid recipient email found for message notification in CRA request {}", craRequest.id);
            }

        } catch (Exception e) {
            LOGGER.error("Failed to send message notification email for CRA request {}", craRequest.id, e);
        }
    }

    /**
     * Check if the user has SCI-related roles
     */
    private boolean isSciRelatedRole(JubiqSession session) {
        if (session.groupName != null) {
            String groupName = session.groupName.toLowerCase();
            return groupName.contains("sci") ||
                   groupName.equals("sci manager") ||
                   groupName.equals("sci staff");
        }
        return false;
    }

    /**
     * Build email body for message notification
     */
    private String buildMessageEmailBody(CraRequest craRequest, KeyvisualMessage message, String senderName) {
        StringBuilder emailBody = new StringBuilder();
        emailBody.append("<h3>New Message in CRA Request</h3>");
        emailBody.append("<p><strong>CRA Request ID:</strong> ").append(craRequest.id).append("</p>");
        emailBody.append("<p><strong>Exposition Detail:</strong> ").append(
            craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request"
        ).append("</p>");
        emailBody.append("<p><strong>From:</strong> ").append(senderName).append("</p>");
        emailBody.append("<p><strong>Message:</strong></p>");
        emailBody.append("<div style=\"border-left: 3px solid #ccc; padding-left: 15px; margin: 10px 0;\">")
                 .append(message.message)
                 .append("</div>");

        // Add link to view the CRA request (you may need to adjust the URL based on your frontend)
        emailBody.append("<p><a href=\"http://localhost:3005/cra/view/").append(craRequest.id)
                 .append("\">Click here to view the CRA request</a></p>");

        return emailBody.toString();
    }

    public void cancel(Long id, String reason, JubiqSession session) throws JubiqPersistenceException {
        CraRequest craRequest = this.dao.get(id);

        // Check if user has permission to cancel (follower or assignee)
//        if (!session.userId.equals(craRequest.followerId) && !session.userId.equals(craRequest.assigneeId)) {
//            throw new UnauthorizedException("You are not authorized to cancel this CRA request");
//        }

        // Check if status allows cancellation (cannot cancel if already completed)
        if (CRAStatus.COMPLETED.equals(craRequest.status) || CRAStatus.CANCEL.equals(craRequest.status)) {
            throw new InvalidRequestException("Cannot cancel CRA request with status: " + craRequest.status);
        }

        // Set cancellation date and change status
        craRequest.status = CRAStatus.CANCEL;

        // Update the request
        this.dao.update(craRequest);

        // Create history record with reason
        String historyNote = reason != null && !reason.trim().isEmpty() ? "Reason: " + reason : null;
        historyService.createHistory(new History(id, "CRA", "cancel", session.userId, session.fullName, null, null, historyNote));

        // Create alert for relevant parties
        Alert alert = new Alert(session.userId, session.fullName, "cancel-cra-request");
        alert.params.put("craRequestId", id.toString());
        alert.params.put("expositionDetail", craRequest.expositionDetail != null ? craRequest.expositionDetail : "CRA Request");
        if (reason != null && !reason.trim().isEmpty()) {
            alert.params.put("reason", reason);
        }

        // Alert follower if canceller is assignee, and vice versa
        if (session.userId.equals(craRequest.followerId) && craRequest.assigneeId != null) {
            alertService.createAlert(alert, null, craRequest.assigneeId);
        } else if (session.userId.equals(craRequest.assigneeId) && craRequest.followerId != null) {
            alertService.createAlert(alert, null, craRequest.followerId);
        }
    }


}
