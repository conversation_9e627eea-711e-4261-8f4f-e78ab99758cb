package com.jubiq.loreal.labels.endpoints;

import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.labels.models.LabelNote;
import com.jubiq.loreal.labels.services.LabelNoteService;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import io.dropwizard.auth.Auth;


import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

@Path("/api/label-notes")
@Api("Label Notes")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class LabelNoteEndpoint extends JubiqEndpoint<Long, LabelNote> {
    private LabelNoteService labelNoteService;

    @Inject
    public LabelNoteEndpoint(LabelNoteService labelNoteService) {
        this.service = this.labelNoteService = labelNoteService;
    }

    @GET
    @Path("/query")
    @ApiOperation("Query labelNotes by label")
    public Response findByNotification(@Auth JubiqSession session, @QueryParam("labelId") Long labelId) {
        List<LabelNote> labelNotes = this.labelNoteService.findByLabel(labelId);
        return Response.ok(labelNotes).build();
    }
}
