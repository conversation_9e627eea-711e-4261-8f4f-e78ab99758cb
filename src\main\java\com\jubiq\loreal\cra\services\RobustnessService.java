package com.jubiq.loreal.cra.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.cra.models.CraRobustness;

/**
 * Created by HH on 7/7/25.
 * Service for Robustness management
 */
@Singleton
public class RobustnessService extends JubiqService<Long, CraRobustness> {
    
    @Inject
    public RobustnessService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(CraRobustness.class, Long.class);
    }
}
