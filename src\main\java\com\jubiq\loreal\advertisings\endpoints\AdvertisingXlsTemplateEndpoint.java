package com.jubiq.loreal.advertisings.endpoints;

import com.jubiq.loreal.advertisings.models.AdvertisingXlsTemplate;
import com.jubiq.loreal.advertisings.services.AdvertisingXlsTemplateService;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;

@Path("/api/advertising-xls-templates")
@Api("Export Advertising Templates")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class AdvertisingXlsTemplateEndpoint extends JubiqEndpoint<Long, AdvertisingXlsTemplate> {
    @Inject
    public AdvertisingXlsTemplateEndpoint(AdvertisingXlsTemplateService advertisingXlsTemplateService) {
        this.service = advertisingXlsTemplateService;
    }
}