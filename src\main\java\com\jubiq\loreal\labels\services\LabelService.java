package com.jubiq.loreal.labels.services;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.labels.models.LabelStatus;
import com.jubiq.loreal.notifications.models.*;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.notifications.services.ExportService;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.notifications.services.PresentationDetailService;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.jubiq.loreal.LorealConstants.*;

import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.util.*;
import java.util.stream.Collectors;
import com.google.inject.Provider;

/**
 * Created by THANHLX on 5/17/2018.
 */
@Singleton
public class LabelService extends JubiqService<Long, Label> {
    protected static Logger LOGGER = LoggerFactory.getLogger(LabelService.class);

    private final Provider<NotificationService> notificationServiceProvider;
    private HistoryService historyService;
    private UserService userService;
    private AlertService alertService;
    private ObjectMapper jsonMapper;
    private ExportService exportService;
    @Inject
    private PresentationDetailService presentationDetailService;

    @Inject
    public LabelService(DaoFactory daoFactory,
                        Provider<NotificationService> notificationServiceProvider,
                        HistoryService historyService,
                        UserService userService,
                        AlertService alertService,
                        ExportService exportService
    ) {
        this.dao = daoFactory.getLabelDao();
        this.notificationServiceProvider = notificationServiceProvider;
        this.historyService = historyService;
        this.userService = userService;
        this.alertService = alertService;
        this.exportService = exportService;
        jsonMapper = new ObjectMapper();
        jsonMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Override
    public Label create(Label entity, JubiqSession session) throws JubiqPersistenceException {
        if (entity.status == null) {
            entity.status = LabelStatus.WAIT_SUBMISSION;
        }
        entity.creatorId = entity.followerId = session.userId;
        beforeCreate(entity);
        if (entity.notificationIds != null && entity.notificationIds.get(0) != null ) {
            NotificationService notificationService = notificationServiceProvider.get();
            Notification notification = notificationService.getDao().get(entity.notificationIds.get(0));
            entity.groupId = notification.groupId;
            entity.assigneeId = notification.assigneeId;
            entity.assigneeEmail = notification.assigneeEmail;
            entity.assigneeFullName = notification.assigneeFullName;
            List<PresentationDetail> presentationDetails =  presentationDetailService.findByNotificationId(entity.notificationIds.get(0));
            if (presentationDetails != null && !presentationDetails.isEmpty()){
                entity.presentationDetailIds = new ArrayList<>();
                presentationDetails.forEach(s -> entity.presentationDetailIds.add(String.valueOf(s.id)));
            }
        }

//        StringBuilder netVolumn = new StringBuilder();
//        StringBuilder productCode = new StringBuilder();
//        String listIdDetail = "("+entity.presentationDetailIds.stream().collect(Collectors.joining(","))+")";
//        List<PresentationDetail> presentationDetailList =  presentationDetailService.findByIdIn(listIdDetail);
//        if(presentationDetailList.size() > 0){
//            entity.notificationIds.forEach(dto ->{
//                netVolumn.append("Request No ").append(dto).append(": ");
//                productCode.append("Request No ").append(dto).append(": ");
//                presentationDetailList.forEach(detail ->{
//                    if(detail.volume != null && detail.volume.length() > 0 && detail.netWeight != null && detail.netWeight.length() > 0){
//                        netVolumn.append(detail.volume).append("/").append(detail.netWeight).append(",");
//                    }
//                    else if(detail.volume != null && detail.volume.length() > 0){
//                        netVolumn.append(detail.volume).append(",");
//                    }
//                    else if(detail.netWeight != null && detail.netWeight.length() > 0){
//                        netVolumn.append(detail.netWeight).append(",");
//                    }
//                    productCode.append(detail.productCode).append(",");
//                });
//            });
//            entity.netVolume = netVolumn.substring(0,netVolumn.length()-1);
//            entity.sapCode = productCode.substring(0,productCode.length()-1);
//        }
        Label label = this.dao.create(entity);
        if (session != null) {
            historyService.createHistory(new History(label.id, "label", "create", session.userId, session.fullName, null, null, null));
        }
        return label;
    }

    @Override
    public void update(Long aLong, Label entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        Label existing = get(aLong);
//        StringBuilder netVolumn = new StringBuilder();
//        StringBuilder productCode = new StringBuilder();
//        String listIdDetail = "("+entity.presentationDetailIds.stream().collect(Collectors.joining(","))+")";
//        List<PresentationDetail> presentationDetailList =  presentationDetailService.findByIdIn(listIdDetail);
//        if(presentationDetailList.size() > 0){
//            for(int i = 0; i< entity.notificationIds.size() ; i++){
//                netVolumn.append("Request No ").append(entity.notificationIds.get(i)).append(": ");
//                productCode.append("Request No ").append(entity.notificationIds.get(i)).append(": ");
//                for(int j = 0; j< presentationDetailList.size() ; j++){
//                    PresentationDetail detail = presentationDetailList.get(j);
//                    if(detail.notificationId.equals(entity.notificationIds.get(i))){
//                        if(detail.volume != null && detail.volume.length() > 0 && detail.netWeight != null && detail.netWeight.length() > 0){
//                            netVolumn.append(detail.volume).append("/").append(detail.netWeight).append(",");
//                        }
//                        else if(detail.volume != null && detail.volume.length() > 0){
//                            netVolumn.append(detail.volume).append(",");
//                        }
//                        else if(detail.netWeight != null && detail.netWeight.length() > 0){
//                            netVolumn.append(detail.netWeight).append(",");
//                        }
//                        productCode.append(detail.productCode).append(",");
//                    }
//                }
//                productCode = productCode.deleteCharAt(productCode.length()-1).append("\n");
//                netVolumn = netVolumn.deleteCharAt(netVolumn.length()-1).append("\n");
//            };
//            entity.netVolume = netVolumn.substring(0,netVolumn.length()-1);
//            entity.sapCode = productCode.substring(0,productCode.length()-1);
//        }
        this.dao.update(entity);
        Label updated = get(aLong);

        String oldValue = null;
        String newValue = null;
        try {
            oldValue = jsonMapper.writeValueAsString(existing);
            newValue = jsonMapper.writeValueAsString(updated);

        } catch (JsonProcessingException e) {
            LOGGER.info("cannot parse json string");
        }

        historyService.createHistory(new History(aLong, "label", "update", session.userId, session.fullName, oldValue, newValue, null));

        String differFields = entity.compareFields(existing).stream().collect(Collectors.joining(", "));
        if(!differFields.isEmpty()) {
            Alert alert = new Alert(session.userId,session.fullName,"update-label");
            alert.params.put("labelId",aLong.toString());
            alert.params.put("differFields",differFields);
            alertService.createAlert(alert, null, updated.followerId);
        }

    }

    @Override
    public void delete(Long aLong, JubiqSession session) throws JubiqPersistenceException {
        Label label = this.dao.get(aLong);
        if (label.status == LabelStatus.VALIDATED) {
            throw new InvalidRequestException("Invalid status, VALIDATED can't delete");
        }
        this.dao.delete(aLong);
        historyService.createHistory(new History(aLong, "label", "delete", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "delete-label");
        alert.params.put("labelId", label.id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);

        alertService.createAlert(alert, null, label.followerId);
    }

    @Override
    protected void validateEntity(Label entity) {
        if (entity.notificationIds == null || entity.notificationIds.size() == 0) {
            throw new InvalidRequestException("Miss notification");
        }

        if (entity.followerId != null) {
            User follower = userService.getDao().get(entity.followerId);
            entity.followerEmail = follower.email;
            entity.followerFullName = follower.fullName;
        }

        if (entity.assigneeId != null) {
            User assignee = userService.getDao().get(entity.assigneeId);
            entity.assigneeEmail = assignee.email;
            entity.assigneeFullName = assignee.fullName;
        }

        if (entity.creatorId != null) {
            User creator = userService.getDao().get(entity.creatorId);
            entity.creatorEmail = creator.email;
            entity.creatorFullName = creator.fullName;
        }
    }

    public void submit(Long id, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.userId.equals(label.followerId)) {
            throw new UnauthorizedException("You are not follower of the notification");
        }
        if (label.status == LabelStatus.VALIDATED) {
            throw new InvalidRequestException("Invalid status, VALIDATED can't submit");
        }
        label.submittedDate = Calendar.getInstance().getTime();
        label.status = LabelStatus.WAIT_VALIDATION;
        label.sciValidated = null;
        label.sciValidatedDate = null;
        label.logisticValidated = null;
        label.logisticValidatedDate = null;
        this.dao.update(label);

        historyService.createHistory(new History(id, "label", "submit", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "submit-label");
        alert.params.put("labelId", id.toString());

        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);

        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, LOGISTICS_GROUP_ID, null);

        Alert sciAlert = new Alert(session.userId, session.fullName, "submit-label");
        sciAlert.params.put("labelId", id.toString());
        sciAlert.params.put("notificationId", notiIds);
        sciAlert.params.put("productName", notification.productName);
        if (label.assigneeId == null) {
            alertService.createAlert(sciAlert, SCI_MANAGER_GROUP_ID, null);
        } else {
            alertService.createAlert(sciAlert, null, label.assigneeId);
        }
    }

    public void validate(Long id, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(label.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this label");
        }
        if (label.status != LabelStatus.WAIT_VALIDATION && label.status != LabelStatus.LOGISTIC_VALIDATED) {
            throw new InvalidRequestException("Invalid status, must be WAIT_VALIDATION or LOGISTIC_VALIDATED");
        }
        label.sciValidated = true;
        label.sciValidatedDate = Calendar.getInstance().getTime();
        if (label.logisticValidated != null && label.logisticValidated) {
            label.status = LabelStatus.VALIDATED;
        } else {
            label.status = LabelStatus.SCI_VALIDATED;
        }
        this.dao.update(label);
        historyService.createHistory(new History(id, "label", "validate", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "validate-label");
        alert.params.put("labelId", id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, label.followerId);

        Alert logisticAlert = new Alert(session.userId, session.fullName, "validate-label");
        logisticAlert.params.put("labelId", id.toString());
        logisticAlert.params.put("notificationId", notiIds);
        logisticAlert.params.put("productName", notification.productName);
        alertService.createAlert(logisticAlert, LOGISTICS_GROUP_ID, null);
    }

    public void logisticValidate(Long id, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.groupId.equals(LOGISTICS_GROUP_ID)) {
            throw new UnauthorizedException("You are not assigned to process this label");
        }
        if (label.status != LabelStatus.WAIT_VALIDATION && label.status != LabelStatus.SCI_VALIDATED) {
            throw new InvalidRequestException("Invalid status, must be WAIT_VALIDATION or SCI_VALIDATED");
        }
        label.logisticValidated = true;
        label.logisticValidatedDate = Calendar.getInstance().getTime();
        if (label.sciValidated != null && label.sciValidated) {
            label.status = LabelStatus.VALIDATED;
        } else {
            label.status = LabelStatus.LOGISTIC_VALIDATED;
        }
        this.dao.update(label);
        historyService.createHistory(new History(id, "label", "logistic-validate", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "logistic-validate-label");
        alert.params.put("labelId", id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, label.followerId);

        Alert sciAlert = new Alert(session.userId, session.fullName, "logistic-validate-label");
        sciAlert.params.put("labelId", id.toString());
        sciAlert.params.put("notificationId", notiIds);
        sciAlert.params.put("productName", notification.productName);
        if (label.assigneeId == null) {
            alertService.createAlert(sciAlert, SCI_MANAGER_GROUP_ID, null);
        } else {
            alertService.createAlert(sciAlert, null, label.assigneeId);
        }
    }

    public void reject(Long id, String reason, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(label.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this notification");
        }

        label.sciValidated = false;
        label.sciValidatedDate = null;
        label.logisticValidated = false;
        label.logisticValidatedDate = null;
        label.status = LabelStatus.REJECTED;
        this.dao.update(label);
        historyService.createHistory(new History(id, "label", "reject", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "reject-label");
        alert.params.put("labelId", id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        alertService.createAlert(alert, LOGISTICS_GROUP_ID, label.followerId);
    }

    public void logisticReject(Long id, String reason, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.groupId.equals(LOGISTICS_GROUP_ID)) {
            throw new UnauthorizedException("You are not assigned to process this notification");
        }

        label.sciValidated = false;
        label.sciValidatedDate = null;
        label.logisticValidated = false;
        label.logisticValidatedDate = null;
        label.status = LabelStatus.REJECTED;
        this.dao.update(label);
        historyService.createHistory(new History(id, "label", "logistic-reject", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "logistic-reject-label");
        alert.params.put("labelId", id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        alertService.createAlert(alert, null, label.followerId);

        Alert sciAlert = new Alert(session.userId, session.fullName, "logistic-reject-label");
        sciAlert.params.put("labelId", id.toString());
        sciAlert.params.put("notificationId", notiIds);
        sciAlert.params.put("productName", notification.productName);
        sciAlert.params.put("reason", reason);
        if (label.assigneeId == null) {
            alertService.createAlert(sciAlert, SCI_MANAGER_GROUP_ID, null);
        } else {
            alertService.createAlert(sciAlert, null, label.assigneeId);
        }
    }

    public boolean isNotificationHasLabel(Long notificationId) {
        StringBuilder sb = new StringBuilder();
//        sb.append("notification_id =" + notificationId);
        sb.append(" FIND_IN_SET('").append(notificationId).append("', REPLACE(REPLACE(REPLACE(notification_ids, '[', ''), ']', ''), ' ', '')) > 0 and deleted is null ");
        try {
            int numberLabel = this.dao.count(sb.toString());
            return numberLabel > 0 ? true : false;
        } catch (Exception ex) {
            LOGGER.error("isNotificationHasLabel. {}", ex.getMessage(), ex);
        }
        return false;
    }

    public List<Long> findIdsByNotificationId(Long notificationId) {
        StringBuilder sb = new StringBuilder("select distinct(id) from labels where ");
//        sb.append("notification_id=:notificationId and deleted is null ");
        sb.append(" FIND_IN_SET('").append(notificationId).append("', REPLACE(REPLACE(REPLACE(notification_ids, '[', ''), ']', ''), ' ', '')) > 0 and deleted is null ");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("notificationId", notificationId);
        List<Long> ids = new ArrayList<>();
        try {
            ids = this.dao.selectLongID(sb.toString(), map);
        } catch (Exception ex) {
            LOGGER.error("findByNotificationId. {}", ex.getMessage(), ex);
        }
        return ids;
    }

    public List<Label> findByNotificationId(Long notificationId) {
        StringBuilder sb = new StringBuilder("select * from labels where ");
//        sb.append("notification_id=:notificationId and deleted is null ");
        sb.append(" FIND_IN_SET('").append(notificationId).append("', REPLACE(REPLACE(REPLACE(notification_ids, '[', ''), ']', ''), ' ', '')) > 0 and deleted is null ");
        sb.append("order by created desc");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("notificationId", notificationId);
        List<Label> labels = new ArrayList<>();
        try {
//            Notification notification = notificationService.get(notificationId);
            labels = this.dao.search(sb.toString(), map);
//            for (Label label : labels) {
//                label.presentationDetails = new ArrayList<>();
//                for (PresentationDetail presentationDetail : notification.presentationDetails) {
//                    if (presentationDetail.id != null && label.presentationDetailIds != null && label.presentationDetailIds.contains(presentationDetail.id.toString())) {
//                        label.presentationDetails.add(presentationDetail);
//                    }
//                }
//            }
            for (Label label : labels) {
                label.presentationDetails = new ArrayList<>();
                label.notificationIds.forEach(dto -> {
                    NotificationService notificationService = notificationServiceProvider.get();
                    Notification notification = notificationService.get(dto);
                    for (PresentationDetail presentationDetail : notification.presentationDetails) {
                        if (presentationDetail.id != null && label.presentationDetailIds != null && label.presentationDetailIds.contains(presentationDetail.id.toString())) {
                            label.presentationDetails.add(presentationDetail);
                        }
                    }
                });
            }
        } catch (Exception ex) {
            LOGGER.error("findByNotificationId. {}", ex.getMessage(), ex);
        }
        return labels;
    }

    public List<Label> findByPresentationDetailId(Long presentationDetailId) {
        StringBuilder sb = new StringBuilder("select * from labels where ");
        sb.append("presentation_detail_ids is not null and presentation_detail_ids like '%" + presentationDetailId + "%' ");
        return this.dao.search(sb.toString());
    }

    public File export(Long id) throws IOException {
        Label label = this.dao.get(id);
        List<Long> list = label.notificationIds;
        List<Notification> listNoti = new ArrayList<>();
        list.forEach(dto -> {
            NotificationService notificationService = notificationServiceProvider.get();
            listNoti.add(notificationService.get(dto));
        });
        File file = null;
        if(listNoti.size() > 1)
            file = exportService.exportLabelBundle(label,listNoti);
        else
            file = exportService.exportLabelSingle(label,listNoti);
        return file;
    }

    public void recall(Long id, String reason, JubiqSession session) {
        Label label = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(label.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }

        label.status = LabelStatus.RECALLED;

        if ((label.sciValidated != null && label.sciValidated) || (label.logisticValidated != null && label.logisticValidated)) {
            label.status = LabelStatus.WAIT_FOR_REJECTION;
        }

        this.dao.update(label);

        historyService.createHistory(new History(id, "label", "recall", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "recall-label");
        alert.params.put("labelId", id.toString());
        NotificationService notificationService = notificationServiceProvider.get();
        Notification notification = notificationService.get(label.notificationIds.get(0));
        String notiIds = label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", "));
        alert.params.put("notificationId", notiIds);
        alert.params.put("productName", notification.productName);

        if (label.assigneeId == null) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        } else {
            alertService.createAlert(alert, null, label.assigneeId);
        }
    }

    public File exportXls(String sheetName, List<Label> labels, List<String> fieldNames, Long callerId) throws IOException {

        File file = exportService.exportLabelsToXls(sheetName, labels, fieldNames);
        return file;
    }

    @Override
    public Label get(Long aLong) throws JubiqPersistenceException {
        Label l = super.get(aLong);
        l.presentationDetails = new ArrayList<>();
        l.notificationPresentationDetails = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        l.notificationIds.forEach(dto -> {
            NotificationService notificationService = notificationServiceProvider.get();
            Notification notification = notificationService.get(dto);
            for (PresentationDetail presentationDetail : notification.presentationDetails) {
                for (Ingredient ingredient : presentationDetail.ingredients) {
                    sb.append(ingredient.fullName).append("\n");
                }
                if (l.presentationDetailIds != null){
                    for (String presentationId : l.presentationDetailIds) {
                        if (presentationDetail.id.toString().equals(presentationId)) {
                            l.presentationDetails.add(presentationDetail);
                        }
                    }
                }

            }
            l.notificationPresentationDetails.addAll(notification.presentationDetails);
        });
        return l;
    }

    public void updateFollowerLabel(String labelId,Long newFollowerId, String newFollowerFullName, String newFollowerEmail ) {
        String sql = "update labels set follower_id=:follower_id, follower_email=:follower_email, follower_full_name=:follower_full_name " +
                "where id = "+ labelId;

        Map<String,Object> map = new HashMap<String,Object>();
        map.put("follower_id",newFollowerId);
        map.put("follower_full_name",newFollowerFullName);
        map.put("follower_email",newFollowerEmail);
        LOGGER.info("Query update label: " + sql);
        this.dao.executeUpdate(sql,map);
    }
}
