package com.jubiq.loreal;

import com.google.inject.Injector;
import com.hubspot.dropwizard.guice.GuiceBundle;
import com.jubiq.loreal.common.endpoints.errorhandlers.AuthenticationExceptionHandler;
import com.jubiq.loreal.common.endpoints.errorhandlers.JubiqExceptionHandler;
import com.jubiq.loreal.common.endpoints.errorhandlers.ValidationExceptionHandler;
import com.jubiq.loreal.guice.LorealModule;
import com.jubiq.loreal.notifications.services.DataInjector;
import com.jubiq.loreal.quartz.job.CfsInciExpireJob;
import com.jubiq.loreal.quartz.job.NotificationExpireJob;
import com.jubiq.loreal.quartz.job.RenameTitleExpireJob;
import com.jubiq.loreal.security.JubiqAuthenticator;
import com.jubiq.loreal.common.model.JubiqSession;
import io.dropwizard.Application;
import io.dropwizard.auth.AuthDynamicFeature;
import io.dropwizard.auth.AuthValueFactoryProvider;
import io.dropwizard.auth.oauth.OAuthCredentialAuthFilter;
import io.dropwizard.db.PooledDataSourceFactory;
import io.dropwizard.jdbi.jersey.LoggingDBIExceptionMapper;
import io.dropwizard.jdbi.jersey.LoggingSQLExceptionMapper;
import io.dropwizard.jersey.errors.EarlyEofExceptionMapper;
import io.dropwizard.jersey.jackson.JsonProcessingExceptionMapper;
import io.dropwizard.migrations.MigrationsBundle;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import io.federecio.dropwizard.swagger.SwaggerBundle;
import io.federecio.dropwizard.swagger.SwaggerBundleConfiguration;
import org.glassfish.jersey.media.multipart.MultiPartFeature;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.SimpleScheduleBuilder.simpleSchedule;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * Created by vietnq2 on 11/12/15.
 */
public class LorealApplication extends Application<LorealConfiguration> {

    public GuiceBundle<LorealConfiguration> guiceBundle;
    private static Logger LOGGER = LoggerFactory.getLogger(LorealApplication.class);


    public static void main(String[] args) throws Exception {
        new LorealApplication().run(args);
    }
    @Override
    public String getName() {
        return "loreal";
    }

    @Override
    public void initialize(Bootstrap<LorealConfiguration> bootstrap) {
        bootstrap.addBundle(new MigrationsBundle<LorealConfiguration>() {
            @Override
            public PooledDataSourceFactory getDataSourceFactory(LorealConfiguration lorealConfiguration) {
                return lorealConfiguration.database;
            }
        });

        guiceBundle = GuiceBundle.<LorealConfiguration>newBuilder()
                .addModule(new LorealModule())
                .enableAutoConfig(getClass().getPackage().getName())
                .setConfigClass(LorealConfiguration.class)
                .build();
        bootstrap.addBundle(guiceBundle);

        bootstrap.addBundle(new SwaggerBundle<LorealConfiguration>() {
            @Override
            protected SwaggerBundleConfiguration getSwaggerBundleConfiguration(LorealConfiguration lorealConfiguration) {
                return lorealConfiguration.swaggerBundleConfiguration;
            }
        });
    }

    @Override
    public void run(LorealConfiguration lorealConfiguration, Environment environment) throws Exception {
//        doImport();
        registerAuthFilter(environment);
        registerExceptionMapper(environment);
        //enable FileUpload
        environment.jersey().register(MultiPartFeature.class);
        registerQuartz();
        System.out.println("STARTED......");
    }

    private void registerAuthFilter(Environment environment) {
        Injector injector = guiceBundle.getInjector();
        environment.jersey().register(new AuthDynamicFeature(
                new OAuthCredentialAuthFilter.Builder<JubiqSession>()
                        .setAuthenticator(injector.getInstance(JubiqAuthenticator.class))
                        .setPrefix("Bearer")
                        .buildAuthFilter()));
        environment.jersey().register(new AuthValueFactoryProvider.Binder<JubiqSession>(JubiqSession.class));
    }

    private void registerExceptionMapper(Environment environment) {
        environment.jersey().register(JubiqExceptionHandler.class);
        environment.jersey().register(ValidationExceptionHandler.class);
        environment.jersey().register(AuthenticationExceptionHandler.class);
        environment.jersey().register(LoggingDBIExceptionMapper.class);
        environment.jersey().register(LoggingSQLExceptionMapper.class);
        environment.jersey().register(JsonProcessingExceptionMapper.class);
        environment.jersey().register(EarlyEofExceptionMapper.class);
    }

    private void doImport() {
        Injector injector = guiceBundle.getInjector();
        DataInjector dataInjector = injector.getInstance(DataInjector.class);
        dataInjector.prepareData();
    }

    private void registerQuartz(){
        initNotificationExpire();
        initCfsInciExpire();
        initRenameTitleExpireInYear();
    }

    private void initNotificationExpire() {
        JobKey jobKey = new JobKey("Notification expire job");
        Scheduler scheduler = guiceBundle.getInjector().getProvider(Scheduler.class).get();
        try {
            if (scheduler.getJobDetail(jobKey) != null) {
                LOGGER.info("Exist notification expire job");
            } else {
                JobDetail job = newJob(NotificationExpireJob.class)
                        .withIdentity("Notification expire job")
                        .build();
                Trigger trigger = newTrigger()
                        .withIdentity("Notification expire trigger")
                        .startNow()
                        .withSchedule(cronSchedule("0 0 8 1 * ? *"))
                        .build();
                scheduler.scheduleJob(job, trigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    private void initCfsInciExpire() {
        JobKey jobKey = new JobKey("CfsInci expire job");
        Scheduler scheduler = guiceBundle.getInjector().getProvider(Scheduler.class).get();
        try {
            if (scheduler.getJobDetail(jobKey) != null) {
                LOGGER.info("Exist CfsInci expire job");
            } else {
                JobDetail job = newJob(CfsInciExpireJob.class)
                        .withIdentity("CfsInci expire job")
                        .build();
                Trigger trigger = newTrigger()
                        .withIdentity("CfsInci expire trigger")
                        .startNow()
                        .withSchedule(cronSchedule("0 0 8 * * ? *"))
                        .build();
                scheduler.scheduleJob(job, trigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }
    private void initRenameTitleExpireInYear() {
        JobKey jobKey = new JobKey("Rename Title Expire In Year Job");
        Scheduler scheduler = guiceBundle.getInjector().getProvider(Scheduler.class).get();
        try {
            if (scheduler.getJobDetail(jobKey) != null) {
                LOGGER.info("Exist rename Title Expire In Year Job");
            } else {
                JobDetail job = newJob(RenameTitleExpireJob.class)
                        .withIdentity("Rename Title Expire In Year Job")
                        .build();
                Trigger trigger = newTrigger()
                        .withIdentity("Rename Title Expire In Year Job Trigger")
                        .startNow()
                        .withSchedule(cronSchedule("0 0 0 1 1 ? *"))
                        .build();
                scheduler.scheduleJob(job, trigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }
}
