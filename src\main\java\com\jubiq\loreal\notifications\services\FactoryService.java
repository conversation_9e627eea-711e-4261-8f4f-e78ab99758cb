package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Country;
import com.jubiq.loreal.notifications.models.Factory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/5/16.
 */
@Singleton
public class FactoryService extends JubiqService<Long,Factory> {
    private CountryService countryService;
    @Inject
    public FactoryService(DaoFactory daoFactory, CountryService countryService) {
        this.dao = daoFactory.createDao(Factory.class, Long.class);
        this.countryService = countryService;
    }

    public Factory findByName(String name) {
        String sql = "select * from factories where name=:name and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("name",name);
        List<Factory> factories = this.dao.search(sql,map);
        if(factories != null && factories.size() > 0) {
            return factories.get(0);
        } else {
            throw new EntityNotFoundException("No factory found with name: " + name);
        }
    }

    public List<Factory> factoriesOfCountry(final Long countryId) {
        String sql = "select * from factories where country_id=:countryId and deleted is null order by name desc";
        Map<String,Object> map = new HashMap<String, Object>(){{put("countryId",countryId);}};
        List<Factory> factories = this.dao.search(sql,map);
        return factories;
    }

    @Override
    protected void validateEntity(Factory factory) {
        Country country = countryService.get(factory.countryId);
        factory.countryName = country.name;
    }

    @Override
    public void afterUpdate(Long id,Factory entity) throws JubiqPersistenceException {
        updateManufacturerInfo(entity);
        updateAssemblerInfo(entity);
        updateExporterInfo(entity);
    }

    private void updateManufacturerInfo(Factory entity) {
        StringBuilder sb = new StringBuilder("update presentation_details ");
        sb.append("set manufacturer_name=:manufacturerName")
                .append(",manufacturer_address=:manufacturerAddress")
                .append(",manufacturer_phone_number=:manufacturerPhoneNumber")
                .append(",manufacturer_fax_number=:manufacturerFaxNumber")
                .append(",manufacturer_country_id=:manufacturerCountryId")
                .append(",manufacturer_country_name=:manufacturerCountryName")
                .append(",updated=:updated")
                .append(" where manufacturer_id=:manufacturerId and deleted is null");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("manufacturerName",entity.name);
        map.put("manufacturerAddress",entity.address);
        map.put("manufacturerPhoneNumber",entity.phoneNumber);
        map.put("manufacturerFaxNumber",entity.faxNumber);
        map.put("manufacturerCountryId",entity.countryId);
        map.put("manufacturerCountryName",entity.countryName);
        map.put("updated",System.currentTimeMillis());
        map.put("manufacturerId",entity.id);
        this.dao.executeUpdate(sb.toString(),map);
    }

    private void updateAssemblerInfo(Factory entity) {
        StringBuilder sb = new StringBuilder("update presentation_details ");
        sb.append("set assembler_name=:assemblerName")
                .append(",assembler_address=:assemblerAddress")
                .append(",assembler_phone_number=:assemblerPhoneNumber")
                .append(",assembler_fax_number=:assemblerFaxNumber")
                .append(",assembler_country_id=:assemblerCountryId")
                .append(",assembler_country_name=:assemblerCountryName")
                .append(",updated=:updated")
                .append(" where assembler_id=:assemblerId and deleted is null");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("assemblerName",entity.name);
        map.put("assemblerAddress",entity.address);
        map.put("assemblerPhoneNumber",entity.phoneNumber);
        map.put("assemblerFaxNumber",entity.faxNumber);
        map.put("assemblerCountryId",entity.countryId);
        map.put("assemblerCountryName",entity.countryName);
        map.put("updated",System.currentTimeMillis());
        map.put("assemblerId",entity.id);
        this.dao.executeUpdate(sb.toString(),map);
    }

    private void updateExporterInfo(Factory entity) {
        StringBuilder sb = new StringBuilder("update presentation_details ");
        sb.append("set exporter_name=:exporterName")
                .append(",exporter_address=:exporterAddress")
                .append(",exporter_phone_number=:exporterPhoneNumber")
                .append(",exporter_fax_number=:exporterFaxNumber")
                .append(",exporter_country_id=:exporterCountryId")
                .append(",exporter_country_name=:exporterCountryName")
                .append(",updated=:updated")
                .append(" where exporter_id=:exporterId and deleted is null");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("exporterName",entity.name);
        map.put("exporterAddress",entity.address);
        map.put("exporterPhoneNumber",entity.phoneNumber);
        map.put("exporterFaxNumber",entity.faxNumber);
        map.put("exporterCountryId",entity.countryId);
        map.put("exporterCountryName",entity.countryName);
        map.put("updated",System.currentTimeMillis());
        map.put("exporterId",entity.id);
        this.dao.executeUpdate(sb.toString(),map);
    }
}
