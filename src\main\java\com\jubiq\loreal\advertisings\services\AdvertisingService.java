package com.jubiq.loreal.advertisings.services;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.advertisings.models.AdStatus;

import static com.jubiq.loreal.LorealConstants.*;
import static com.jubiq.loreal.LorealConstants.SCI_MANAGER_GROUP_ID;
import static com.jubiq.loreal.advertisings.models.AdStatus.*;
import com.jubiq.loreal.advertisings.models.Advertising;
import com.jubiq.loreal.advertisings.models.KeyvisualMessage;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.FileService;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;

import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.models.History;
import com.jubiq.loreal.notifications.models.Notification;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.notifications.services.ExportService;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.GroupService;
import com.jubiq.loreal.umgr.services.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonProcessingException;


/**
 * Created by vietnq on 11/29/15.
 */
@Singleton
public class AdvertisingService extends JubiqService<Long,Advertising> {
    protected static Logger LOGGER = LoggerFactory.getLogger(AdvertisingService.class);

    private SimpleDateFormat dateFormater = new SimpleDateFormat("YYYY-MM-dd");
    private SimpleDateFormat dateTimeFormater = new SimpleDateFormat("YYYY-MM-dd HH:mm:ss");
    private UserService userService;
    private GroupService groupService;
    private HistoryService historyService;
    private AlertService alertService;
    private FileService fileService;
    private ObjectMapper jsonMapper;
    private ExportService exportService;

    private MailService mailService;

    @Inject
    private NotificationService notificationService;

    @Inject
    public AdvertisingService(DaoFactory daoFactory,
                              UserService userService,
                              GroupService groupService,
                              HistoryService historyService,
                              AlertService alertService,
                              FileService fileService,
                              ExportService exportService,
                              MailService mailService) {
        this.dao = daoFactory.createDao(Advertising.class,Long.class);
        this.userService = userService;
        this.groupService = groupService;
        this.historyService = historyService;
        this.alertService = alertService;
        this.fileService = fileService;
        this.exportService = exportService;
        this.notificationService=  notificationService;
        this.mailService = mailService;
        jsonMapper = new ObjectMapper();
        jsonMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Override
    public Advertising create(Advertising entity, JubiqSession session) throws JubiqPersistenceException {
        if(entity.status == null) {
            entity.status = WAIT_MKT_SUBMISSION;
        }
        entity.creatorId = entity.followerId = session.userId;
        if(entity.id == null) {
            Long currentId = currentIncrementValue();
            entity.id = currentId;
        }
        beforeCreate(entity);
        if(entity.requestIds.size() > 0) {
            int notificationId = entity.requestIds.get(0);
            Notification notification = notificationService.getDao().get(Long.valueOf(notificationId));
            entity.groupId = notification.groupId;
            entity.assigneeId = notification.assigneeId;
            entity.assigneeEmail = notification.assigneeEmail;
            entity.assigneeFullName = notification.assigneeFullName;
            entity.brandId = notification.brandId;
            entity.brandName = notification.brandName;
        }
        Advertising advertising = this.dao.create(entity);
        if(session != null) {
            historyService.createHistory(new History(advertising.id,"advertising","create",session.userId,session.fullName,null,null,null));
        }
        return advertising;
    }

    private Long currentIncrementValue() {
        String sql = "select auto_increment from information_schema.TABLES where TABLE_SCHEMA='loreal' and TABLE_NAME='advertisings'";
        return this.dao.selectLong(sql);
    }

    @Override
    public void update(Long aLong, Advertising entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        Advertising existing = get(aLong);

        if(entity.requestIds.size() > 0) {
            int notificationId = entity.requestIds.get(0);
            Notification notification = notificationService.getDao().get(Long.valueOf(notificationId));
            entity.groupId = notification.groupId;
            entity.assigneeId = notification.assigneeId;
            entity.assigneeEmail = notification.assigneeEmail;
            entity.assigneeFullName = notification.assigneeFullName;
            entity.brandId = notification.brandId;
            entity.brandName = notification.brandName;
        }
        sendEmailToCreator(entity);
        this.dao.update(entity);
        Advertising updated = get(aLong);

        String oldValue = null;
        String newValue = null;
        try {
            oldValue = jsonMapper.writeValueAsString(existing);
            newValue = jsonMapper.writeValueAsString(updated);

        } catch (JsonProcessingException e) {
            LOGGER.info("cannot parse json string");
        }

        historyService.createHistory(new History(aLong,"advertising","update",session.userId,session.fullName,oldValue,newValue,null));

        String differFields = entity.compareFields(existing).stream().collect(Collectors.joining(", "));
        if(!differFields.isEmpty()) {
            Alert alert = new Alert(session.userId,session.fullName,"update-advertising");
            alert.params.put("advertisingId",aLong.toString());
            alert.params.put("differFields",differFields);
            alertService.createAlert(alert, null, updated.followerId);
        }
    }

    @Override
    protected void validateEntity(Advertising entity) {
        if(entity.followerId != null) {
            User follower = userService.getDao().get(entity.followerId);
            entity.followerEmail = follower.email;
            entity.followerFullName = follower.fullName;
        }
        if(entity.assigneeId != null) {
            User assignee = userService.getDao().get(entity.assigneeId);
            entity.assigneeEmail = assignee.email;
            entity.assigneeFullName = assignee.fullName;
        }
        if(entity.creatorId != null) {
            User creator = userService.getDao().get(entity.creatorId);
            entity.creatorEmail = creator.email;
            entity.creatorFullName = creator.fullName;
        }
    }

    public void submitToSci(Long id, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(!session.userId.equals(advertising.followerId)) {
            throw new UnauthorizedException("You are not follower of the advertising");
        }
        if(advertising.status != WAIT_MKT_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_MKT_SUBMISSION");
        }

        Calendar submittedCal = Calendar.getInstance();
        Date submittedDate = submittedCal.getTime();
        advertising.submittedDate = submittedDate;

        advertising.status = WAIT_VALIDATION;
        this.dao.update(advertising);

        historyService.createHistory(new History(id, "advertising", "submit", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId,session.fullName,"submit-advertising");
        alert.params.put("advertisingId",id.toString());

        if (advertising.assigneeId == null) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        }
        else{
            alertService.createAlert(alert, null, advertising.assigneeId);
        }
    }


    public void revise(Long id, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(!session.userId.equals(advertising.followerId)) {
            throw new UnauthorizedException("You are not follower of the advertising");
        }
        if(advertising.status != REJECTED && advertising.status != RECALLED) {
            throw new InvalidRequestException("Invalid status, must be REJECTED or RECALLED");
        }

        Calendar revisingCal = Calendar.getInstance();
        Date revisingDate = revisingCal.getTime();
        advertising.revisingDate = revisingDate;

        advertising.status = WAIT_VALIDATION;
        sendEmailToCreator(advertising);
        this.dao.update(advertising);

        historyService.createHistory(new History(id, "advertising", "revise", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId,session.fullName,"revise-advertising");
        alert.params.put("advertisingId",id.toString());

        if (advertising.assigneeId == null) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        }
        else{
            alertService.createAlert(alert, null, advertising.assigneeId);
        }
    }

    public void validate(Long id, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(advertising.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this advertising");
        }
        if(advertising.status != WAIT_VALIDATION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_VALIDATION");
        }
        advertising.validated = true;
        advertising.validatedTime = Calendar.getInstance().getTime();
        advertising.status = WAIT_SUBMISSION;

        this.dao.update(advertising);
        historyService.createHistory(new History(id, "advertising", "validate", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId,session.fullName,"validate-advertising");
        alert.params.put("advertisingId", id.toString());
        alertService.createAlert(alert, null, advertising.followerId);
    }

    public void reject(Long id, String reason, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(advertising.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this advertising");
        }
        if(advertising.status == COMPLETED || advertising.status == REJECTED) {
            throw new InvalidRequestException("Invalid status, must not be COMPLETED or REJECTED");
        }
        advertising.validated = false;
        advertising.status = REJECTED;
        this.dao.update(advertising);
        historyService.createHistory(new History(id,"advertising","reject",session.userId,session.fullName,null,null,reason));

        Alert alert = new Alert(session.userId,session.fullName,"reject-advertising");
        alert.params.put("advertisingId", id.toString());
        alert.params.put("reason", reason);
        alertService.createAlert(alert, null, advertising.followerId);
    }

    public void recall(Long id, String reason, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(advertising.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }

        advertising.status = AdStatus.RECALLED;

        if(advertising.validated != null && advertising.validated){
            advertising.status = AdStatus.WAIT_FOR_REJECTION;
        }

        this.dao.update(advertising);

        historyService.createHistory(new History(id, "advertising", "recall", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId,session.fullName,"recall-advertising");
        alert.params.put("advertisingId",id.toString());
        alert.params.put("reason", reason);

        if (advertising.assigneeId == null) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        }
        else{
            alertService.createAlert(alert, null, advertising.assigneeId);
        }
    }


    @Override
    public void delete(Long aLong, JubiqSession session) throws JubiqPersistenceException {
        Advertising advertising = this.dao.get(aLong);
        if(advertising.status == COMPLETED) {
            throw new InvalidRequestException("Invalid status, COMPLETED can't delete");
        }
        this.dao.delete(aLong);
        historyService.createHistory(new History(aLong,"advertising","delete",session.userId,session.fullName,null,null,null));

        Alert alert = new Alert(session.userId,session.fullName,"delete-advertising");
        alert.params.put("advertisingId",advertising.id.toString());

        alertService.createAlert(alert, null, advertising.followerId);
    }

    public void submitLicense(Long id, Date licenseRequestingDate, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(advertising.status != WAIT_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_SUBMISSION");
        }
        advertising.licenseRequestingDate = licenseRequestingDate;
        advertising.status = WAIT_COMPLETED;
        sendEmailToCreator(advertising);
        this.dao.update(advertising);
        historyService.createHistory(new History(id, "advertising", "submit-license", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId,session.fullName,"submit-license-advertising");
        alert.params.put("advertisingId", id.toString());
        alertService.createAlert(alert, null, advertising.followerId);
    }

    public void receiveLicense(Long id, Date licenseReceivingDate, Date licenseExpiredDate, String licenseNumber, List<String> licenseFiles, List<String> otherFiles, List<String> proofDocuments, JubiqSession session) {
        Advertising advertising = this.dao.get(id);
        if(advertising.status != WAIT_COMPLETED) {
            throw new InvalidRequestException("Invalid status, must be WAIT_COMPLETED");
        }
        advertising.status = COMPLETED;
        advertising.licenseReceivingDate = licenseReceivingDate;
        advertising.licenseExpiredDate = licenseExpiredDate;
        advertising.licenseNumber = licenseNumber;

        Map<String,String> mapLicenseFiles = new HashMap<String,String>();
        for(String fileId : licenseFiles) {
            mapLicenseFiles.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        advertising.licenseFiles = mapLicenseFiles;

        Map<String,String> mapOtherFiles = new HashMap<>();
        for(String fileId : otherFiles){
            mapOtherFiles.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        advertising.otherFiles = mapOtherFiles;

        Map<String, String> mapProofDocuments = new HashMap<>();
        for(String fileId : proofDocuments){
            mapProofDocuments.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        advertising.proofDocuments = mapProofDocuments;

        this.dao.update(advertising);
        historyService.createHistory(new History(id, "advertising", "receive-license", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId,session.fullName,"receive-license-advertising");
        alert.params.put("advertisingId", id.toString());
        alertService.createAlert(alert, null, advertising.followerId);
    }

    public File exportXls(String sheetName, List<Advertising> labels, List<String> fieldNames, Long callerId) throws IOException {

        File file = exportService.exportAdvertisingsToXls(sheetName, labels, fieldNames);
        return file;
    }

    public List<Advertising> findByNotificationId(Long notificationId) {
        StringBuilder sb = new StringBuilder("select * from advertisings where ");
        sb.append("(request_ids like '%," + notificationId + ",%' or request_ids = '[" +notificationId +"]'" + " or request_ids LIKE '%[" + notificationId + ",%' or request_ids like '%," + notificationId + "]%') and deleted is null ");
        sb.append("order by created desc");
        String query = sb.toString();
        return this.dao.search(query);
    }

    @Override
    public Advertising get(Long id) throws JubiqPersistenceException {
        Advertising advertising = dao.get(id);
        if(advertising.advertisementDetail == null) advertising.advertisementDetail = "DoH";
        if(advertising.channel == null) advertising.channel = "";
        if(advertising.valueOtherChannel == null) advertising.valueOtherChannel = "";
        if(advertising.timeline == null) advertising.timeline = dateTimeFormater.format(new Date());
        if(advertising.advertisementType == null) advertising.advertisementType = "KV";
        return advertising;
    }

    private void sendEmailToCreator(Advertising advertising){
        try {
            String emailCreator = advertising.creatorEmail;
            if(advertising.messages != null && advertising.messages.size() > 0){
                for(KeyvisualMessage keyvisualMessage : advertising.messages){
                    if(Strings.isNullOrEmpty(keyvisualMessage.createdTime)){
                        try {
                            List<String> fileNames = new ArrayList<>();
                            if(keyvisualMessage.attachmentFiles != null){
                                for(String key : keyvisualMessage.attachmentFiles.keySet()){
                                    fileNames.add(fileService.get(Long.parseLong(key)).uri);
                                }
                            }
                            String body = String.format("You have message \"%s\" in <a href=\"http://loreal.com/advertising/process/%d\">advertisement</a>",
                                                        keyvisualMessage.message, advertising.id);
                            mailService.sendMail(emailCreator, "HAS MESSAGE IN ADVERTISEMENT", body, fileNames.toArray(new String[fileNames.size()]));
                        }catch (Exception e){
                            LOGGER.error(e.getMessage(), e);
                        }
                        keyvisualMessage.createdTime = dateFormater.format(new Date());
                    }
                }
            }
        }catch (Exception e){
            LOGGER.error(e.getMessage(), e);
        }
    }
}
