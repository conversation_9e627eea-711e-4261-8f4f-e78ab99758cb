package com.jubiq.loreal.umgr.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.GroupNode;
import com.jubiq.loreal.umgr.models.GroupsPermissions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 11/30/15.
 */
@Singleton
public class GroupService extends JubiqService<Long,Group> {
    private JubiqDao<Long,GroupsPermissions> groupsPermissionsDao;
    @Inject
    public GroupService(DaoFactory daoFactory) {
        this.daoFactory = daoFactory;
        this.dao = daoFactory.createDao(Group.class,Long.class);
        this.groupsPermissionsDao = this.daoFactory.createDao(GroupsPermissions.class,Long.class);
    }


    public GroupNode groupTree(String order) throws JubiqPersistenceException {
        String sql = "select * from groups where deleted is null";
        order = (order == null) ? "created desc" : order;
        sql += " order by " + order;

        List<Group> groups = this.dao.search(sql);
        for(Group group : groups) {
            group.permissions = groupPermissions(group.id);
        }
        return groupNode(0L,groups);
    }

    public GroupNode groupNode(Long id, List<Group> groups) {
        GroupNode node = new GroupNode();
        for(Group group : groups) {
            if(group.id.equals(id)) {
                node.group = group;
            }
            if(group.ascendantId.equals(id)) {
                node.children.add(groupNode(group.id, groups));
            }
        }
        return node;
    }

    public List<String> groupPermissions(Long groupId) throws JubiqPersistenceException {
        Group group = this.dao.get(groupId);
        List<String> perms = new ArrayList<String>();
        String query = "select * from groups_permissions where group_id=:groupId or group_id=:ascendantId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("groupId",groupId);
        map.put("ascendantId",group.ascendantId);
        List<GroupsPermissions> groupPerms = groupsPermissionsDao.search(query,map);
        if(groupPerms != null && groupPerms.size() > 0) {
            for(GroupsPermissions groupPerm : groupPerms) {
                perms.add(groupPerm.permissionId);
            }
        }
        return perms;
    }

    @Override
    public Group get(Long aLong) throws JubiqPersistenceException {
        Group group = this.dao.get(aLong);
        group.permissions = groupPermissions(aLong);
        return group;
    }

    @Override
    public Group create(Group entity, JubiqSession session) throws JubiqPersistenceException {
        Group group = this.dao.create(entity);
        group.permissions = entity.permissions;
        for(String permission : group.permissions) {
            this.groupsPermissionsDao.create(new GroupsPermissions(group.id,permission));
        }
        return group;
    }

    @Override
    public void update(Long groupId, Group entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        Group existing = get(groupId);
        existing.ascendantId = entity.ascendantId;
        existing.name = entity.name;
        //update permission
        for(String permission : entity.permissions) {
            if(!existing.permissions.contains(permission)) {
                addGroupPermission(groupId,permission);
            }
        }

        for(String permission : existing.permissions) {
            if(!entity.permissions.contains(permission)) {
                removeGroupPermission(groupId,permission);
            }
        }
        this.dao.update(existing);
    }

    public void addGroupPermission(Long groupId, String permissionId) throws JubiqPersistenceException {
        GroupsPermissions groupsPermission = new GroupsPermissions(groupId,permissionId);
        this.groupsPermissionsDao.create(groupsPermission);
    }

    public void removeGroupPermission(Long groupId, String permissionId) throws JubiqPersistenceException {
        String sql = "delete from groups_permissions where group_id=:groupId and permission_id=:permissionId";
        Map<String,Object> bindMap = new HashMap<String,Object>();
        bindMap.put("groupId",groupId);
        bindMap.put("permissionId",permissionId);
        this.groupsPermissionsDao.executeUpdate(sql,bindMap);
    }

    public void removeGroupPermission(Long groupId) throws JubiqPersistenceException {
        String sql = "delete from groups_permissions where group_id=:groupId";
        Map<String,Object> bindMap = new HashMap<String,Object>();
        bindMap.put("groupId",groupId);
        this.groupsPermissionsDao.executeUpdate(sql,bindMap);
    }

    @Override
    public void delete(Long aLong, JubiqSession session) throws JubiqPersistenceException {
        removeGroupPermission(aLong);
        this.dao.delete(aLong);
        String sql = "delete from users where group_id=:groupId";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("groupId",aLong);
        this.dao.executeUpdate(sql,map);
    }

    public Group findByName(final String name) {
        String sql = "select * from groups where name=:name";
        Map<String,Object> map = new HashMap<String, Object>(){{put("name",name);}};
        List<Group> groups = this.dao.search(sql, map);
        if(groups == null || groups.size() == 0) {
            throw new EntityNotFoundException("No group with name " + name);
        }
        return groups.get(0);

    }

    @Override
    public void afterUpdate(Long aLong, Group entity) throws JubiqPersistenceException {
        String sql = "update users set updated=:updated,group_name=:groupName where group_id=:groupId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("groupName",entity.name);
        map.put("groupId",entity.id);
        map.put("updated", System.currentTimeMillis());
        this.dao.executeUpdate(sql, map);
    }
}
