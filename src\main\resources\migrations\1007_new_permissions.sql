--liquibase formatted sql
--changeset vietnq:1007
delete from groups_permissions;
delete from permissions;

INSERT INTO permissions (id,description) VALUES ('*:*','Super permission');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (1,1000,'*:*');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (2,2001,'*:*');

INSERT INTO permissions (id,description) VALUES ('API:USER:CREATE','Allow to create user');
INSERT INTO permissions (id,description) VALUES ('API:USER:UPDATE','Allow to update user');
INSERT INTO permissions (id,description) VALUES ('API:USER:FIND','Allow to find users');
INSERT INTO permissions (id,description) VALUES ('API:USER:DELETE','Allow to delete user');
INSERT INTO permissions (id,description) VALUES ('API:USER:READ','Allow to view user');

INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:CREATE','Allow to create notification');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:UPDATE','Allow to update notification');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:FIND','Allow to find notifications');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:DELETE','Allow to delete notification');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:READ','Allow to view notification');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:ASSIGN','Allow to assign notification');
INSERT INTO permissions (id,description) VALUES ('API:NOTIFICATION:SUBMIT','Allow to submit notification');


--rollback;