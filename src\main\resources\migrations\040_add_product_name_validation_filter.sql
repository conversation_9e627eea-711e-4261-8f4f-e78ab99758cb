--liquibase formatted sql
--changeset thanhlx:040
INSERT INTO `filters` (
    `id`,
    `name`,
    `query`,
    `scope`,
    `class_name`,
    `created`,
    `updated`,
    `deleted`,
    `creator_id`,
    `user_id`)
VALUES (
    'WAIT_FOR_PRODUCT_NAME_VALIDATION',
    'Wait for product name validation',
    'reject_product_name IS NULL',
    'GL<PERSON>BAL',
    'notifications',
    '1455723614408',
    NULL,
    NULL,
    NULL,
    NULL),(
    'REJECTED_PRODUCT_NAME',
    'Rejected product name',
    'reject_product_name = 1',
    'GLOBAL',
    'notifications',
    '1455723614409',
    NULL,
    NULL,
    NULL,
    NULL);
--rollback;