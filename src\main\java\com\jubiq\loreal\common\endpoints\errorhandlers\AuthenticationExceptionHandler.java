package com.jubiq.loreal.common.endpoints.errorhandlers;

import com.jubiq.loreal.common.exceptions.ErrorMessage;
import com.jubiq.loreal.common.exceptions.JubiqException;
import io.dropwizard.auth.AuthenticationException;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 12/21/15.
 */
public class AuthenticationExceptionHandler extends JubiqExceptionMapper<AuthenticationException> {
    @Override
    protected ErrorMessage getErrorMessage(AuthenticationException e) {
        ErrorMessage message = new ErrorMessage();
        message.errorCode = ((JubiqException)e.getCause()).getErrorCode();
        message.errorMessage = e.getCause().getMessage();
        return message;
    }

    @Override
    protected Response.Status getStatus(AuthenticationException e) {
        return ((JubiqException)e.getCause()).getStatus();
    }
}
