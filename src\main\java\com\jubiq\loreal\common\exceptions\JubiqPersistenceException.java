package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 11/18/15.
 */
public class JubiqPersistenceException extends JubiqException {

    public JubiqPersistenceException(Throwable e) {
        super(e);
    }

    public JubiqPersistenceException(String message) {
        super(message);
    }

    public JubiqPersistenceException(String message, Throwable e) {
        super(message,e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.JUBIQ_PERSISTENCE.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.JUBIQ_PERSISTENCE.name();
    }
}
