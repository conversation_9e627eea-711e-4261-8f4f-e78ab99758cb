package com.jubiq.loreal.common.endpoints;

import com.google.inject.Singleton;
import com.jubiq.loreal.common.service.MailService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;

import javax.inject.Inject;
import javax.ws.rs.*;

import javax.ws.rs.core.Response;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by THANHLX on 1/29/2019.
 */
@Path("/api/mail")
@Api("mail")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
@Singleton
public class MailEndpoint {
    private static Logger LOGGER = LoggerFactory.getLogger(MailEndpoint.class);
    private MailService mailService;

    @Inject
    public MailEndpoint(MailService mailService) {
        this.mailService = mailService;
    }

    @POST
    @Path("/send-email")
    @ApiOperation("send email")
    public Response doSendMail(@FormParam("to") String to,
                                     @FormParam("subject") String subject,
                                     @FormParam("body") String body) {
        mailService.sendMail(to,subject,body,null,null);
        return Response.ok().build();
    }
}
