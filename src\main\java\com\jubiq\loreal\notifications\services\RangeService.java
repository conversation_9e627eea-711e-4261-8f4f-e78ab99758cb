package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Brand;
import com.jubiq.loreal.notifications.models.Range;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 1/7/16.
 */
@Singleton
public class RangeService extends JubiqService<Long,Range> {
    JubiqDao<Long,Brand> brandDao;
    @Inject
    public RangeService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(Range.class, Long.class);
        this.brandDao = daoFactory.createDao(Brand.class, Long.class);
    }

    public List<Range> rangesOfBrand(Long brandId) {
        String sql = "select * from ranges where brand_id=:brandId and deleted is null order by name asc";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("brandId",brandId);
        List<Range> ranges = this.dao.search(sql,map);
        for(Range range : ranges) {
            range.brand = brandDao.get(range.brandId);
        }
        return ranges;
    }

    @Override
    public Range get(Long aLong) throws JubiqPersistenceException {
        Range range = super.get(aLong);
        range.brand = brandDao.get(range.brandId);
        return range;
    }

    @Override
    public List<Range> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        List<Range> ranges = super.search(query, limit, offset, order);
        for(Range range : ranges) {
           range.brand = brandDao.get(range.brandId);
        }
        return ranges;
    }

    @Override
    protected void validateEntity(Range entity) {
        brandDao.get(entity.brandId);
    }

    @Override
    public void afterUpdate(Long id,Range entity) throws JubiqPersistenceException {
        String sql = "update notifications set product_range_name=:name,updated=:updated where product_range_id=:productRangeId and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("name",entity.name);
        map.put("productRangeId",entity.id);
        this.dao.executeUpdate(sql,map);
    }

    public Range findByNameAndBrand(String name, Long brandId) {
        String sql = "select * from ranges where name=:name and brand_id=:brandId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("name",name);
        map.put("brandId",brandId);
        List<Range> ranges = this.dao.search(sql,map);
        if(ranges == null || ranges.size() == 0) {
            throw new EntityNotFoundException("No range with name: " + name + " and brand id " + brandId);
        }
        return ranges.get(0);
    }
}
