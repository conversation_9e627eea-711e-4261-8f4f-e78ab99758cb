package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq on 12/25/15.
 */
public class InvalidForgotPasswordTokenException extends JubiqException {
    public InvalidForgotPasswordTokenException(Throwable e) {
        super(e);
    }

    public InvalidForgotPasswordTokenException(String message) {
        super(message);
    }

    public InvalidForgotPasswordTokenException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.INVALID_FORGOT_PWD_TOKEN.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.INVALID_FORGOT_PWD_TOKEN.name();
    }
}
