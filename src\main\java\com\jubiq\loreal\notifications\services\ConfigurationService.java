package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.Configuration;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;

/**
 * Created by vietnq on 12/26/15.
 */
@Singleton
public class ConfigurationService extends JubiqService<String,Configuration> {

    @Inject
    public ConfigurationService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(Configuration.class, String.class);
    }

    public String getCreateUserEmail(String password) {
        String template = get("create.user.email.template").value;
        return String.format(template, password);
    }

    public String getForgotPasswordEmail(Long userId, String token) throws JubiqPersistenceException {
        String template = get("forgot.password.email.template").value;
        return String.format(template, userId, token);
    }

    public String getAlertEmail(String action) {
        String templateId = action + ".alert.email.template";
        String template = "";
        try {
            template = get(templateId).value;
        } catch(Exception e) {
            //no template found, return empty
        }
        return template;
    }
}
