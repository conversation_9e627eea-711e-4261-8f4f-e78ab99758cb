--liquibase formatted sql

--changeset vietnq:3
CREATE TABLE filters (
  id VARCHAR(255),
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  query MEDIUMTEXT,
  scope VARCHAR(16),
  class_name VARCHAR(255),
  created BIGIN<PERSON>,
  updated BIGIN<PERSON>,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
);

CREATE TABLE users_filters (
  id INTEGER,
  filter_id VARCHAR(255),
  user_id INTEGER,
  created BIGINT,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  PRIMARY KEY (id)
)
--rollback drop table jubiq_filters, user_filters;