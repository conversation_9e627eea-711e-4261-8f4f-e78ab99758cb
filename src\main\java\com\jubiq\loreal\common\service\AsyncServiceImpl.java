package com.jubiq.loreal.common.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;

import java.util.concurrent.*;

/**
 * Created by vietnq on 12/25/15.
 */
@Singleton
public class AsyncServiceImpl implements AsyncService {
    private static final int THREAD_POOL_SIZE = 15;
    private ExecutorService executorService;

    @Inject
    public AsyncServiceImpl() {
//        executorService =
//                Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        executorService =
                new ThreadPoolExecutor(15, 20, 0L, TimeUnit.MILLISECONDS,
                        new ArrayBlockingQueue(10000));
    }
    @Override
    public void execute(Runnable runnable) {
        executorService.submit(runnable);
    }
}
