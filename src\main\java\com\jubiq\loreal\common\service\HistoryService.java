package com.jubiq.loreal.common.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.notifications.models.History;
import com.jubiq.loreal.umgr.models.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 1/21/16.
 */
@Singleton
public class HistoryService extends JubiqService<Long,History> {
    private Logger logger = LoggerFactory.getLogger(HistoryService.class);
    private AsyncService asyncService;
    private JubiqDao<Long,User> userDao;
    @Inject
    public HistoryService(DaoFactory daoFactory, AsyncService asyncService) {
        this.dao = daoFactory.createDao(History.class,Long.class);
        this.asyncService = asyncService;
        this.userDao = daoFactory.createDao(User.class,Long.class);
    }

    public void createHistory(History history) {
        NewHistoryTask task = new NewHistoryTask(history);
        this.asyncService.execute(task);
    }

    public List<History> findByNotificationId(Long notificationId) {
        StringBuilder sb = new StringBuilder("select * from histories where ");
        sb.append("object_id=:objectId and object_name='notification' and deleted is null ");
        sb.append("order by created desc");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("objectId",notificationId);
        return this.dao.search(sb.toString(),map);
    }

    public List<History> findByLabelId(Long labelId) {
        StringBuilder sb = new StringBuilder("select * from histories where ");
        sb.append("object_id=:objectId and object_name='label' and deleted is null ");
        sb.append("order by created desc");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("objectId",labelId);
        return this.dao.search(sb.toString(),map);
    }

    public List<History> findByAdvertisingId(Long advertisingId) {
        StringBuilder sb = new StringBuilder("select * from histories where ");
        sb.append("object_id=:objectId and object_name='advertising' and deleted is null ");
        sb.append("order by created desc");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("objectId",advertisingId);
        return this.dao.search(sb.toString(),map);
    }

    public List<History> findByCraId(Long craId) {
        StringBuilder sb = new StringBuilder("select * from histories where ");
        sb.append("object_id=:objectId and object_name='CRA' and deleted is null ");
        sb.append("order by created desc");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("objectId",craId);
        return this.dao.search(sb.toString(),map);
    }
    private class NewHistoryTask implements Runnable {
        private Logger logger = LoggerFactory.getLogger(NewHistoryTask.class);
        public History history;
        public NewHistoryTask(History history) {
            this.history = history;
        }
        @Override
        public void run() {
            logger.info("Running history task in {}",Thread.currentThread().getName());
            HistoryService.this.create(history, null);
        }
    }
 }
