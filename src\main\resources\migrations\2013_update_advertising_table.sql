--liquibase formatted sql
--changeset thanhlx:2013

ALTER TABLE `advertisings` DROP `keyvisual`;
ALTER TABLE `advertisings` ADD `description` TEXT NULL AFTER `license_expired_date`, ADD `content_files` TEXT NULL AFTER `description`, ADD `references_files` TEXT NULL AFTER `content_files`, ADD `messages` TEXT NULL AFTER `references_files`;
ALTER TABLE `advertisings` ADD `other_files` TEXT NULL DEFAULT NULL AFTER `messages`;
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('recall-advertising.alert.email.template', 'Advertising ${advertisingId} is recalled by ${sourceFullName}. <br> Please click <a href="http://***********/advertising/process/${advertisingId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);
ALTER TABLE `advertisings` ADD `brand_id` INT NULL AFTER `other_files`, ADD `brand_name` VARCHAR(255) NULL AFTER `brand_id`, ADD `revising_date` DATE NULL AFTER `brand_name`;