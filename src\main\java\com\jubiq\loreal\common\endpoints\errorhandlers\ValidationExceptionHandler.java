package com.jubiq.loreal.common.endpoints.errorhandlers;

import com.jubiq.loreal.common.exceptions.ErrorMessage;
import com.jubiq.loreal.common.exceptions.JubiqErrorType;

import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 12/18/15.
 */
public class ValidationExceptionHandler extends JubiqExceptionMapper<ConstraintViolationException> {
    @Override
    protected ErrorMessage getErrorMessage(ConstraintViolationException e) {
        ErrorMessage message = new ErrorMessage();
        message.errorCode = JubiqErrorType.VALIDATION_FAILED.name();
        message.errorMessage = e.getConstraintViolations().iterator().next().getMessage();
        return message;
    }

    @Override
    protected Response.Status getStatus(ConstraintViolationException e) {
        return JubiqErrorType.VALIDATION_FAILED.httpStatus;
    }
}
