# 📖 Hướng dẫn sử dụng hệ thống đăng nhập nhanh cho GM/CM

## 🎯 Tổng quan

Hệ thống đăng nhập nhanh cho phép GM (General Manager) v<PERSON> <PERSON><PERSON> (Category Manager) truy cập trực tiếp vào trang approval CRA request mà không cần đăng nhập thủ công. Khi STRD hoàn thành risk assessment, hệ thống sẽ tự động gửi email chứa link đăng nhập nhanh đến GM và CM.

## 🔄 Luồng hoạt động chi tiết

### **Bước 1: STRD hoàn thành Risk Assessment**

1. **STRD đăng nhập** vào hệ thống CRA
2. **Mở CRA request** có status `WAIT_FOR_STRD_RISK_ASSESSMENT`
3. **Thực hiện đánh giá rủi ro** và điền thông tin cần thiết
4. **<PERSON><PERSON> nút "Save Risk Assessment"**

**<PERSON><PERSON> thống tự động thực hiện:**
- ✅ Chuyển status thành `WAIT_FOR_APPROVAL`
- ✅ <PERSON><PERSON> nhận thời gian `dateOfStradRiskAssessment`
- ✅ Tạo 2 mã băm unique:
  - `gmLoginHash`: `a1b2c3d4-e5f6-7890-abcd-ef1234567890`
  - `cmLoginHash`: `z9y8x7w6-v5u4-3210-zyxw-vu9876543210`
- ✅ Gửi email approval đến GM và CM

### **Bước 2: GM/CM nhận email approval**

**Email sẽ có nội dung:**
```html
Dear General Manager,

CRA Request 123 (TV Commercial Campaign) has completed risk assessment and requires your approval.

Please click here to review and approve/reject this request.
[APPROVE/REJECT BUTTON]

This link will automatically log you in for quick approval.

Best regards,
L'Oreal CRA System
```

**Link trong email:**
```
http://localhost:3005/cra/quick-login.html?craRequestId=123&loginHash=a1b2c3d4-e5f6-7890-abcd-ef1234567890&email=<EMAIL>
```

### **Bước 3: GM/CM click vào link approval**

#### **3.1. Frontend xử lý link**
Khi GM/CM click vào link, frontend sẽ:

1. **Parse URL parameters:**
   ```javascript
   const urlParams = new URLSearchParams(window.location.search);
   const craRequestId = urlParams.get('craRequestId'); // 123
   const loginHash = urlParams.get('loginHash');       // a1b2c3d4-e5f6-7890...
   const email = urlParams.get('email');               // <EMAIL>
   ```

2. **Gọi API đăng nhập nhanh:**
   ```javascript
   const response = await fetch('/api/cra-quick-login/authenticate', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/x-www-form-urlencoded',
     },
     body: `craRequestId=${craRequestId}&loginHash=${loginHash}&email=${email}`
   });
   
   const session = await response.json();
   ```

3. **Lưu session token:**
   ```javascript
   localStorage.setItem('accessToken', session.id);
   localStorage.setItem('userInfo', JSON.stringify(session));
   ```

4. **Redirect đến trang approval:**
   ```javascript
   window.location.href = `/cra/approval/${craRequestId}`;
   ```

#### **3.2. Backend xử lý authentication**

**API Call:**
```bash
POST /api/cra-quick-login/authenticate
Content-Type: application/x-www-form-urlencoded

craRequestId=123&loginHash=a1b2c3d4-e5f6-7890-abcd-ef1234567890&email=<EMAIL>
```

**Backend process:**
1. ✅ Validate parameters
2. ✅ Tìm CRA request với ID = 123
3. ✅ So sánh `loginHash` với `gmLoginHash` trong database
4. ✅ Tìm user thuộc GM group (groupId = 2)
5. ✅ Tạo session với thông tin:
   ```json
   {
     "id": "quick-session-uuid-12345",
     "userId": 456,
     "userEmail": "<EMAIL>",
     "fullName": "John Doe (Quick Login GM)",
     "grantType": "quick_login",
     "expiresIn": 7200,
     "permissions": ["API:CRA:QUICK_LOGIN", "API:CRA:APPROVE"],
     "groupId": 2,
     "groupName": "General Manager"
   }
   ```

### **Bước 4: Trang approval hiển thị**

Frontend sử dụng session token để load trang approval:

```javascript
// Load CRA request details
const response = await fetch(`/api/cra-requests/${craRequestId}`, {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});

const craRequest = await response.json();
```

**Trang approval hiển thị:**
- 📋 Thông tin CRA request
- 📄 Chi tiết risk assessment
- 📎 File đính kèm
- ✅ Nút "Approve"
- ❌ Nút "Reject"
- 💬 Ô nhập comments/reason

### **Bước 5: GM/CM thực hiện approval/rejection**

#### **5.1. Approve CRA Request**

**Frontend:**
```javascript
const approveRequest = async (craRequestId, comments) => {
  const response = await fetch(`/api/cra-requests/${craRequestId}/approve`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `comments=${encodeURIComponent(comments)}`
  });
  
  if (response.ok) {
    alert('CRA Request approved successfully!');
    // Redirect hoặc refresh page
  }
};
```

**Backend process:**
1. ✅ Validate quick login session
2. ✅ Check CRA request status = `WAIT_FOR_APPROVAL`
3. ✅ Update status = `COMPLETED`
4. ✅ Set `dateOfCompleted` = current time
5. ✅ Vô hiệu hóa `gmLoginHash` và `cmLoginHash`
6. ✅ Ghi history: "approve by GM (Quick Login)"
7. ✅ Gửi alert đến follower

#### **5.2. Reject CRA Request**

**Frontend:**
```javascript
const rejectRequest = async (craRequestId, reason) => {
  const response = await fetch(`/api/cra-requests/${craRequestId}/reject`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `reason=${encodeURIComponent(reason)}`
  });
  
  if (response.ok) {
    alert('CRA Request rejected successfully!');
  }
};
```

**Backend process:**
1. ✅ Validate quick login session
2. ✅ Check CRA request status = `WAIT_FOR_APPROVAL`
3. ✅ Update status = `WAIT_FOR_STRD_RISK_ASSESSMENT`
4. ✅ Vô hiệu hóa `gmLoginHash` và `cmLoginHash`
5. ✅ Ghi history: "reject by GM (Quick Login)" + reason
6. ✅ Gửi alert đến assignee và follower

## 🔧 Implementation Frontend

### **1. Trang xử lý quick login (quick-login.html)**

```html
<!DOCTYPE html>
<html>
<head>
    <title>CRA Quick Login</title>
</head>
<body>
    <div id="loading">
        <h2>Đang xử lý đăng nhập...</h2>
        <p>Vui lòng đợi trong giây lát.</p>
    </div>

    <script>
        async function handleQuickLogin() {
            try {
                // Parse URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const craRequestId = urlParams.get('craRequestId');
                const loginHash = urlParams.get('loginHash');
                const email = urlParams.get('email');

                if (!craRequestId || !loginHash || !email) {
                    throw new Error('Missing required parameters');
                }

                // Call quick login API
                const response = await fetch('/api/cra-quick-login/authenticate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `craRequestId=${craRequestId}&loginHash=${loginHash}&email=${email}`
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(error);
                }

                const session = await response.json();

                // Save session
                localStorage.setItem('accessToken', session.id);
                localStorage.setItem('userInfo', JSON.stringify(session));

                // Redirect to approval page
                window.location.href = `/cra/approval.html?craRequestId=${craRequestId}`;

            } catch (error) {
                document.getElementById('loading').innerHTML = `
                    <h2>Lỗi đăng nhập</h2>
                    <p style="color: red;">${error.message}</p>
                    <p>Vui lòng liên hệ administrator hoặc thử lại sau.</p>
                `;
            }
        }

        // Auto execute when page loads
        handleQuickLogin();
    </script>
</body>
</html>
```

### **2. Trang approval (approval.html)**

```html
<!DOCTYPE html>
<html>
<head>
    <title>CRA Approval</title>
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .cra-info { background: #f5f5f5; padding: 15px; margin: 10px 0; }
        .actions { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-approve { background: #28a745; color: white; }
        .btn-reject { background: #dc3545; color: white; }
        .comments { width: 100%; height: 100px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRA Request Approval</h1>

        <div id="userInfo"></div>

        <div id="craInfo" class="cra-info">
            <h3>Loading CRA Request...</h3>
        </div>

        <div class="actions">
            <h3>Your Decision:</h3>
            <textarea id="comments" class="comments" placeholder="Enter your comments/reason..."></textarea>
            <br>
            <button id="approveBtn" class="btn btn-approve">✅ Approve</button>
            <button id="rejectBtn" class="btn btn-reject">❌ Reject</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let craRequestId;
        let accessToken;

        async function init() {
            // Get session info
            accessToken = localStorage.getItem('accessToken');
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

            if (!accessToken) {
                document.getElementById('result').innerHTML =
                    '<p style="color: red;">Session expired. Please use the link from email again.</p>';
                return;
            }

            // Display user info
            document.getElementById('userInfo').innerHTML = `
                <p><strong>Logged in as:</strong> ${userInfo.fullName}</p>
                <p><strong>Role:</strong> ${userInfo.groupName}</p>
            `;

            // Get CRA request ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            craRequestId = urlParams.get('craRequestId');

            if (!craRequestId) {
                document.getElementById('result').innerHTML =
                    '<p style="color: red;">Invalid CRA request ID.</p>';
                return;
            }

            // Load CRA request details
            await loadCraRequest();

            // Setup event listeners
            document.getElementById('approveBtn').addEventListener('click', approveCraRequest);
            document.getElementById('rejectBtn').addEventListener('click', rejectCraRequest);
        }

        async function loadCraRequest() {
            try {
                const response = await fetch(`/api/cra-requests/${craRequestId}`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load CRA request');
                }

                const craRequest = await response.json();

                document.getElementById('craInfo').innerHTML = `
                    <h3>CRA Request #${craRequest.id}</h3>
                    <p><strong>Status:</strong> ${craRequest.status}</p>
                    <p><strong>Exposition:</strong> ${craRequest.expositionLevel} - ${craRequest.expositionDetail}</p>
                    <p><strong>Advertisement Type:</strong> ${craRequest.advertisementType}</p>
                    <p><strong>Timeline:</strong> ${craRequest.timeline}</p>
                    <p><strong>Creator:</strong> ${craRequest.creatorFullName}</p>
                    <p><strong>Assignee:</strong> ${craRequest.assigneeFullName}</p>
                    <p><strong>Risk Assessment Date:</strong> ${new Date(craRequest.dateOfStradRiskAssessment).toLocaleString()}</p>
                `;

            } catch (error) {
                document.getElementById('craInfo').innerHTML =
                    `<p style="color: red;">Error loading CRA request: ${error.message}</p>`;
            }
        }

        async function approveCraRequest() {
            const comments = document.getElementById('comments').value;

            if (!confirm('Are you sure you want to approve this CRA request?')) {
                return;
            }

            try {
                const response = await fetch(`/api/cra-requests/${craRequestId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `comments=${encodeURIComponent(comments)}`
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(error);
                }

                document.getElementById('result').innerHTML =
                    '<p style="color: green;">✅ CRA Request approved successfully!</p>';

                // Disable buttons
                document.getElementById('approveBtn').disabled = true;
                document.getElementById('rejectBtn').disabled = true;

            } catch (error) {
                document.getElementById('result').innerHTML =
                    `<p style="color: red;">Error approving request: ${error.message}</p>`;
            }
        }

        async function rejectCraRequest() {
            const reason = document.getElementById('comments').value;

            if (!reason.trim()) {
                alert('Please provide a reason for rejection.');
                return;
            }

            if (!confirm('Are you sure you want to reject this CRA request?')) {
                return;
            }

            try {
                const response = await fetch(`/api/cra-requests/${craRequestId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `reason=${encodeURIComponent(reason)}`
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(error);
                }

                document.getElementById('result').innerHTML =
                    '<p style="color: orange;">❌ CRA Request rejected successfully!</p>';

                // Disable buttons
                document.getElementById('approveBtn').disabled = true;
                document.getElementById('rejectBtn').disabled = true;

            } catch (error) {
                document.getElementById('result').innerHTML =
                    `<p style="color: red;">Error rejecting request: ${error.message}</p>`;
            }
        }

        // Initialize when page loads
        init();
    </script>
</body>
</html>
```

## 🔗 URL Structure

### **1. Link trong email:**
```
http://localhost:3005/cra/quick-login.html?craRequestId=123&loginHash=abc-def-ghi&email=<EMAIL>
```

### **2. Sau khi đăng nhập thành công:**
```
http://localhost:3005/cra/approval.html?craRequestId=123
```

## 🛡️ Bảo mật và Xử lý lỗi

### **Các trường hợp lỗi:**

1. **Hash không hợp lệ:**
   ```json
   {
     "error": "Invalid quick login hash for GM"
   }
   ```

2. **CRA Request không tồn tại:**
   ```json
   {
     "error": "CRA Request not found with id: 123"
   }
   ```

3. **Session hết hạn:**
   ```json
   {
     "error": "Session expired"
   }
   ```

4. **Hash đã được sử dụng:**
   ```json
   {
     "error": "Quick login hash has been invalidated"
   }
   ```

### **Biện pháp bảo mật:**

1. ✅ **One-time use**: Hash bị vô hiệu hóa sau khi sử dụng
2. ✅ **Time-limited**: Session chỉ có hiệu lực 2 giờ
3. ✅ **UUID-based**: Hash không thể đoán được
4. ✅ **Audit trail**: Ghi lại tất cả hành động
5. ✅ **Permission-based**: Chỉ có quyền approve/reject CRA request cụ thể

## 📱 Mobile-friendly

Hệ thống được thiết kế responsive, GM/CM có thể sử dụng trên:
- 💻 Desktop
- 📱 Mobile phone
- 📟 Tablet

## 🎯 Kết luận

Hệ thống đăng nhập nhanh này giúp:
- ⚡ **Tăng tốc độ approval**: Không cần đăng nhập thủ công
- 🔒 **Đảm bảo bảo mật**: Hash unique và one-time use
- 📧 **Tự động hóa**: Gửi email và tạo session tự động
- 📊 **Tracking đầy đủ**: Ghi lại tất cả hành động trong history
- 📱 **Dễ sử dụng**: Interface đơn giản, mobile-friendly

GM và CM chỉ cần click vào link trong email là có thể approve/reject CRA request ngay lập tức!
