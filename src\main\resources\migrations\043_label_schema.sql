--liquibase formatted sql
--changeset thanhlx:043
CREATE TABLE labels (
  id INTEGER NOT NULL,
  created BIGIN<PERSON>,
  updated BIGINT,
  deleted BIGINT,
  creator_id INTEGER,
  creator_email VARCHAR(255),
  creator_full_name VA<PERSON>HAR(255),
  notification_id INTEGER,
  status VARCHAR(255),
  assignee_id INTEGER,
  assignee_email VARCHAR(255),
  assignee_full_name VA<PERSON>HA<PERSON>(255),
  group_id INTEGER,
  follower_id INTEGER,
  follower_email VARCHAR(255),
  follower_full_name VARCHAR(255),
  product_name VARCHAR(255),
  product_name_vn VARCHAR(255),
  user_manual TEXT,
  importer TEXT,
  distributor TEXT,
  alarm TEXT,
  lot_number VARCHAR(255),
  manufacturing_date VARCHAR(255),
  expiration_date VARCHAR(255),
  hotline VARCHAR(32),
  ingredients MEDIUMTEXT,
  dav_notification_number VARCHAR(32),
  manufacturer TEXT,
  preservation TEXT,
  net_volume VARCHAR(255),
  uses TEXT,
  PRIMARY KEY (id)
);
--rollback;