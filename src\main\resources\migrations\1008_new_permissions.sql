--liquibase formatted sql
--changeset vietnq:1008

INSERT INTO permissions(id,description) VALUES ('API:ALERT:READ','Allow to read alert');

INSERT INTO permissions(id,description) VALUES ('API:BRAND:CREATE','Allow to create brand');
INSERT INTO permissions(id,description) VALUES ('API:BRAND:UPDATE','Allow to update brand');
INSERT INTO permissions(id,description) VALUES ('API:BRAND:READ','Allow to read brand');
INSERT INTO permissions(id,description) VALUES ('API:BRAND:DELETE','Allow to delete brand');
INSERT INTO permissions(id,description) VALUES ('API:BRAND:FIND','Allow to find brand');

INSERT INTO permissions(id,description) VALUES ('API:COUNTRY:CREATE','Allow to create country');
INSERT INTO permissions(id,description) VALUES ('API:COUNTRY:UPDATE','Allow to update country');
INSERT INTO permissions(id,description) VALUES ('API:COUNTRY:READ','Allow to read country');
INSERT INTO permissions(id,description) VALUES ('API:COUNTRY:DELETE','Allow to delete country');
INSERT INTO permissions(id,description) VALUES ('API:COUNTRY:FIND','Allow to find country');

INSERT INTO permissions(id,description) VALUES ('API:FACTORY:CREATE','Allow to create factory');
INSERT INTO permissions(id,description) VALUES ('API:FACTORY:UPDATE','Allow to update factory');
INSERT INTO permissions(id,description) VALUES ('API:FACTORY:READ','Allow to read factory');
INSERT INTO permissions(id,description) VALUES ('API:FACTORY:DELETE','Allow to delete factory');
INSERT INTO permissions(id,description) VALUES ('API:FACTORY:FIND','Allow to find factory');

INSERT INTO permissions(id,description) VALUES ('API:FILTER:CREATE','Allow to create filter');
INSERT INTO permissions(id,description) VALUES ('API:FILTER:UPDATE','Allow to update filter');
INSERT INTO permissions(id,description) VALUES ('API:FILTER:READ','Allow to read filter');
INSERT INTO permissions(id,description) VALUES ('API:FILTER:DELETE','Allow to delete filter');
INSERT INTO permissions(id,description) VALUES ('API:FILTER:FIND','Allow to find filter');

INSERT INTO permissions(id,description) VALUES ('API:GROUP:CREATE','Allow to create group');
INSERT INTO permissions(id,description) VALUES ('API:GROUP:UPDATE','Allow to update group');
INSERT INTO permissions(id,description) VALUES ('API:GROUP:READ','Allow to read group');
INSERT INTO permissions(id,description) VALUES ('API:GROUP:DELETE','Allow to delete group');
INSERT INTO permissions(id,description) VALUES ('API:GROUP:FIND','Allow to find group');

INSERT INTO permissions(id,description) VALUES ('API:HISTORY:READ','Allow to read history');
INSERT INTO permissions(id,description) VALUES ('API:HISTORY:FIND','Allow to find history');

INSERT INTO permissions(id,description) VALUES ('API:INGREDIENT:CREATE','Allow to create ingredient');
INSERT INTO permissions(id,description) VALUES ('API:INGREDIENT:UPDATE','Allow to update ingredient');
INSERT INTO permissions(id,description) VALUES ('API:INGREDIENT:READ','Allow to read ingredient');
INSERT INTO permissions(id,description) VALUES ('API:INGREDIENT:DELETE','Allow to delete ingredient');
INSERT INTO permissions(id,description) VALUES ('API:INGREDIENT:FIND','Allow to find ingredient');

INSERT INTO permissions(id,description) VALUES ('API:NOTE:CREATE','Allow to create note');
INSERT INTO permissions(id,description) VALUES ('API:NOTE:UPDATE','Allow to update note');
INSERT INTO permissions(id,description) VALUES ('API:NOTE:READ','Allow to read note');
INSERT INTO permissions(id,description) VALUES ('API:NOTE:DELETE','Allow to delete note');
INSERT INTO permissions(id,description) VALUES ('API:NOTE:FIND','Allow to find note');

INSERT INTO permissions(id,description) VALUES ('API:USERPREFERENCE:CREATE','Allow to create preference');
INSERT INTO permissions(id,description) VALUES ('API:USERPREFERENCE:UPDATE','Allow to update preference');
INSERT INTO permissions(id,description) VALUES ('API:USERPREFERENCE:READ','Allow to read preference');
INSERT INTO permissions(id,description) VALUES ('API:USERPREFERENCE:DELETE','Allow to delete preference');
INSERT INTO permissions(id,description) VALUES ('API:USERPREFERENCE:FIND','Allow to find preference');

INSERT INTO permissions(id,description) VALUES ('API:PRODUCTTYPE:CREATE','Allow to create product type');
INSERT INTO permissions(id,description) VALUES ('API:PRODUCTTYPE:UPDATE','Allow to create update type');
INSERT INTO permissions(id,description) VALUES ('API:PRODUCTTYPE:READ','Allow to read product type');
INSERT INTO permissions(id,description) VALUES ('API:PRODUCTTYPE:DELETE','Allow to delete product type');
INSERT INTO permissions(id,description) VALUES ('API:PRODUCTTYPE:FIND','Allow to find product type');

INSERT INTO permissions(id,description) VALUES ('API:RANGE:CREATE','Allow to create range');
INSERT INTO permissions(id,description) VALUES ('API:RANGE:UPDATE','Allow to update range');
INSERT INTO permissions(id,description) VALUES ('API:RANGE:READ','Allow to read range');
INSERT INTO permissions(id,description) VALUES ('API:RANGE:DELETE','Allow to delete range');
INSERT INTO permissions(id,description) VALUES ('API:RANGE:FIND','Allow to find range');

INSERT INTO permissions(id,description) VALUES ('API:XLSTEMPLATE:CREATE','Allow to create export template');
INSERT INTO permissions(id,description) VALUES ('API:XLSTEMPLATE:UPDATE','Allow to update export template');
INSERT INTO permissions(id,description) VALUES ('API:XLSTEMPLATE:READ','Allow to read export template');
INSERT INTO permissions(id,description) VALUES ('API:XLSTEMPLATE:DELETE','Allow to delete export template');
INSERT INTO permissions(id,description) VALUES ('API:XLSTEMPLATE:FIND','Allow to find export template');
--sci staff
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (5,2002,'API:FACTORY:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (6,2002,'API:FACTORY:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (7,2002,'API:FACTORY:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (8,2002,'API:FACTORY:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (9,2002,'API:FACTORY:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (10,2002,'API:FILTER:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (11,2002,'API:FILTER:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (12,2002,'API:FILTER:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (13,2002,'API:FILTER:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (14,2002,'API:FILTER:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (15,2002,'API:INGREDIENT:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (16,2002,'API:INGREDIENT:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (17,2002,'API:INGREDIENT:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (18,2002,'API:INGREDIENT:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (19,2002,'API:INGREDIENT:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (20,2002,'API:NOTE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (21,2002,'API:NOTE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (22,2002,'API:NOTE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (23,2002,'API:NOTE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (24,2002,'API:NOTE:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (25,2002,'API:USERPREFERENCE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (26,2002,'API:USERPREFERENCE:READ');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (27,2002,'API:RANGE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (28,2002,'API:RANGE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (29,2002,'API:RANGE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (30,2002,'API:RANGE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (31,2002,'API:RANGE:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (32,2002,'API:XLSTEMPLATE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (33,2002,'API:XLSTEMPLATE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (34,2002,'API:XLSTEMPLATE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (35,2002,'API:XLSTEMPLATE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (36,2002,'API:XLSTEMPLATE:DELETE');
--marketing
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (37,3000,'API:FACTORY:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (38,3000,'API:FACTORY:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (39,3000,'API:FACTORY:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (40,3000,'API:FACTORY:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (41,3000,'API:FACTORY:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (42,3000,'API:FILTER:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (43,3000,'API:FILTER:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (44,3000,'API:FILTER:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (45,3000,'API:FILTER:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (46,3000,'API:FILTER:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (47,3000,'API:INGREDIENT:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (48,3000,'API:INGREDIENT:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (49,3000,'API:INGREDIENT:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (50,3000,'API:INGREDIENT:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (51,3000,'API:INGREDIENT:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (52,3000,'API:NOTE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (53,3000,'API:NOTE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (54,3000,'API:NOTE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (55,3000,'API:NOTE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (56,3000,'API:NOTE:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (57,3000,'API:USERPREFERENCE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (58,3000,'API:USERPREFERENCE:READ');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (59,3000,'API:RANGE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (60,3000,'API:RANGE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (61,3000,'API:RANGE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (62,3000,'API:RANGE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (63,3000,'API:RANGE:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (64,3000,'API:XLSTEMPLATE:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (65,3000,'API:XLSTEMPLATE:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (66,3000,'API:XLSTEMPLATE:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (67,3000,'API:XLSTEMPLATE:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (68,3000,'API:XLSTEMPLATE:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (69,3000,'API:NOTIFICATION:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (70,3000,'API:NOTIFICATION:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (71,3000,'API:NOTIFICATION:CREATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (72,3000,'API:NOTIFICATION:FIND');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (73,3000,'API:NOTIFICATION:DELETE');

INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (74,2002,'API:NOTIFICATION:READ');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (75,2002,'API:NOTIFICATION:UPDATE');
INSERT INTO groups_permissions(id,group_id,permission_id) VALUES (76,2002,'API:NOTIFICATION:FIND');


--rollback