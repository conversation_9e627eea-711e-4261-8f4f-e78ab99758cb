package com.jubiq.loreal.quartz.job;

import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.models.Notification;

import com.google.inject.Inject;
import com.jubiq.loreal.notifications.services.AlertService;
import com.jubiq.loreal.umgr.models.User;

import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;


import com.jubiq.loreal.notifications.services.ConfigurationService;
import com.jubiq.loreal.notifications.services.NotificationService;
import com.jubiq.loreal.umgr.services.UserService;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

import static com.jubiq.loreal.LorealConstants.SCI_MANAGER_GROUP_ID;

public class CfsInciExpireJob implements Job {
    @Inject
    private NotificationService notificationService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        notificationService.excuteCfsInciExpireJob();
    }
}