package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq on 12/26/15.
 */
public class InvalidOwnerResourceException extends JubiqException {

    public InvalidOwnerResourceException(String message) {
        super(message);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.INVALID_OWNER_RESOURCE.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.INVALID_OWNER_RESOURCE.name();
    }
}
