package com.jubiq.loreal.common.persistence;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.mapper.GenericMapper;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.common.util.Serializer;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.skife.jdbi.v2.Query;
import org.skife.jdbi.v2.Update;
import org.skife.jdbi.v2.exceptions.DBIException;
import org.skife.jdbi.v2.util.IntegerMapper;
import org.skife.jdbi.v2.util.LongMapper;
import org.skife.jdbi.v2.util.StringMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import static com.jubiq.loreal.common.util.NamingUtil.toSnakeCase;

/**
 * Created by vietnq on 11/15/15.
 */
public class JubiqDao<ID extends Serializable, T extends JubiqEntity<ID>>  {

    private static final int MAX_RESULTS = 500;
    protected static Logger LOGGER = LoggerFactory.getLogger(JubiqDao.class);

    protected DBI dbi;
    protected Class<T> entityClass;
    protected Class<ID> idType;
    protected Map<Field, String> entityFields = new LinkedHashMap<Field, String>();
    protected Map<String,Field> fieldsMap = new LinkedHashMap<String, Field>();
    protected GenericMapper<T> mapper;
    protected String tableName;
    /* Generated SQL statements */
    protected String getSql;
    protected String getAllSql;
    protected String countSql;
    protected String insertSql;
    protected String updateSql;
    protected String removeSql;

    public JubiqDao() {

    }

    public JubiqDao(DBI dbi, Class<T> entityClass, Class<ID> idType) {
        this.dbi = dbi;
        this.entityClass = entityClass;
        this.idType = idType;
        this.tableName = toSnakeCase(entityClass.getSimpleName());

        if(!this.tableName.endsWith("s")) {
            if(this.tableName.endsWith("y")) {
                this.tableName = this.tableName.substring(0,this.tableName.length() - 1);
                this.tableName = this.tableName + "ies";
            } else {
                this.tableName = this.tableName + "s";
            }
            this.tableName = this.tableName.toLowerCase();
        }

        for (Field field : entityClass.getFields()) {
            if(!field.isAnnotationPresent(NotAColumn.class)) {
                entityFields.put(field, toSnakeCase(field.getName()));
                fieldsMap.put(field.getName(),field);
            }
        }

        this.mapper = new GenericMapper<T>(entityClass,entityFields,idType);

        generateSqlQueries();
    }

    public List<T> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        order = (order == null || order.length() == 0) ? "created desc" : order;
        query = (query == null || query.length() == 0) ? "" : " AND " + query;
        StringBuilder sb = new StringBuilder("select * from ").append(tableName)
                .append(" where deleted is NULL")
                .append(query)
                .append(" order by ").append(order)
                .append(" limit ").append(limit)
                .append(" offset ").append(offset);

        String sql = sb.toString();
        Handle handle = dbi.open();
        try {
            Query<T> dbiQuery = handle.createQuery(sql).map(mapper);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }
    public Long selectLong(String sql) throws JubiqPersistenceException {

      Handle handle = dbi.open();
      try {
        Query<Map<String,Object>> query = handle.createQuery(sql);
        return query.map(LongMapper.FIRST).first();
      } catch(Exception e) {
        throw new JubiqPersistenceException(e);
      } finally {
        handle.close();
      }
    }

    public List<String> selectString(String query, Map<String,Object> bindMap) throws JubiqPersistenceException {
      Handle handle = dbi.open();
      try {
        Query<String> dbiQuery = handle.createQuery(query).bindFromMap(bindMap).map(StringMapper.FIRST);
        return dbiQuery.list();
      } catch(Exception e) {
        throw new JubiqPersistenceException(e);
      } finally {
        handle.close();
      }
    }

    public List<String> selectString(String sql) throws JubiqPersistenceException {

      Handle handle = dbi.open();
      try {
        Query<Map<String,Object>> query = handle.createQuery(sql);
        return query.map(StringMapper.FIRST).list();
      } catch(Exception e) {
        throw new JubiqPersistenceException(e);
      } finally {
        handle.close();
      }
    }

    public int selectInt(String query) throws JubiqPersistenceException {
      Handle handle = dbi.open();
      try {
        return handle.createQuery(query).map(IntegerMapper.FIRST).first();
      } catch(Exception e) {
        throw new JubiqPersistenceException(e);
      } finally {
        handle.close();
      }
    }

    public Long selectLong(String query, Map<String,Object> bindMap) throws JubiqPersistenceException {
      Handle handle = dbi.open();
      try {
        Query<Long> dbiQuery = handle.createQuery(query).bindFromMap(bindMap).map(LongMapper.FIRST);
        return dbiQuery.first();
      } catch(Exception e) {
        throw new JubiqPersistenceException(e);
      } finally {
        handle.close();
      }
    }

    public List<Long> selectLongID(String query, Map<String,Object> bindMap) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<Long> dbiQuery = handle.createQuery(query).bindFromMap(bindMap).map(LongMapper.FIRST);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public int count(String whereExp) throws JubiqPersistenceException {

        Handle handle = dbi.open();
        try {
            StringBuilder sb = new StringBuilder(countSql);
            if(whereExp != null && whereExp.length() > 0) {
                sb.append(" AND ").append("(").append(whereExp).append(")");
            }

            Query<Map<String,Object>> query = handle.createQuery(sb.toString());
            return query.map(IntegerMapper.FIRST).first();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public T get(ID id) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<Map<String,Object>> query = handle.createQuery(getSql).bind("id", id);
            Query<T> result = query.map(mapper);
            T entity = result.first();
            if(entity == null) {
                throw new EntityNotFoundException("No " + entityClass.getSimpleName() + " with id " + id);
            }
            return result.first();
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public List<T> getAll() throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<T> query = handle.createQuery(getAllSql).map(mapper);
            query.setMaxRows(MAX_RESULTS);
            return query.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public List<T> search(String query, Map<String,Object> bindMap) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<T> dbiQuery = handle.createQuery(query).bindFromMap(bindMap).map(mapper);
            dbiQuery.setMaxRows(MAX_RESULTS);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public List<T> search(String query) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<T> dbiQuery = handle.createQuery(query).map(mapper);
            dbiQuery.setMaxRows(MAX_RESULTS);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }
    public List<T> search(String query, int limit) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Query<T> dbiQuery = handle.createQuery(query).map(mapper);
            dbiQuery.setMaxRows(limit);
            return dbiQuery.list();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public void update(T entity) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            entity.updated = System.currentTimeMillis();
            Update update = handle.createStatement(updateSql);
            update = update.bindFromMap(mapper.getBindMap(entity));
            update.execute();
        } catch(Exception e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public T create(T entity) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            if(entity.id == null) {
                entity.id = randomId();
            }
            if(entity.created == null) {
                entity.created = System.currentTimeMillis();
            }
            Update update = handle.createStatement(insertSql);
            update = update.bindFromMap(mapper.getBindMap(entity));
            update.execute();
            return entity;
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public void delete(ID id) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Update update = handle.createStatement(removeSql);
            update = update.bind("id",id).bind("deleted",System.currentTimeMillis());
            update.execute();
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public void executeUpdate(String sql, Map<String,Object> bindMap) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        try {
            Update update = handle.createStatement(sql);
            if(bindMap != null) {
                update = update.bindFromMap(bindMap);
            }
            update.execute();
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public void batchDelete(List<ID> ids) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        for(ID id : ids) {
            try {
                Update update = handle.createStatement(removeSql);
                update = update.bind("id",id).bind("deleted",System.currentTimeMillis());
                update.execute();
            } catch(Exception e) {
                throw new JubiqPersistenceException(e);
            }
        }
        handle.close();
    }

    public void updateField(ID id, String fieldName, Object value) throws JubiqPersistenceException{
        Handle handle = dbi.open();
        StringBuilder sb = new StringBuilder("UPDATE ").append(tableName)
                .append(" SET updated=:updated,")
                .append(fieldName).append("=:").append(fieldName)
                .append(" WHERE id=:id");
        try {
            Update update = handle.createStatement(sb.toString());
            update = update.bind("id",id).bind("updated",System.currentTimeMillis()).bind(fieldName,value);
            update.execute();
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    public void updateFields(ID id, Map<String,Object> map) throws JubiqPersistenceException {
        Handle handle = dbi.open();
        if(map.size() == 0) {
            throw new InvalidRequestException("No field to update");
        }
        StringBuilder sb = new StringBuilder("UPDATE ").append(tableName)
                .append(" SET updated=:updated");

        ObjectMapper jsonMapper = new ObjectMapper();
        for(String key : map.keySet()) {
            sb.append(",").append(toSnakeCase(key)).append("=:").append(key);
            Field field = fieldsMap.get(key);
            if (Collection.class.isAssignableFrom(field.getType()) || Map.class.isAssignableFrom(field.getType())) {
                try {
                    map.put(key,jsonMapper.writeValueAsString(map.get(key)));
                } catch (JsonProcessingException e) {
                   throw new InvalidRequestException("Error processing json");
                }
            } else if (field.getType().isEnum()) {
                map.put(key, map.get(key).toString());
            } else if (field.isAnnotationPresent(Serialized.class)) {

                Serialized serialized = field.getAnnotation(Serialized.class);
                Serializer serializer = null;
                try {
                    serializer = (Serializer) serialized.serializer().newInstance();
                } catch (InstantiationException e) {
                    throw new InvalidRequestException("Error doing serialization");
                } catch (IllegalAccessException e) {
                    throw new InvalidRequestException("Error doing serialization");
                }

                try {
                    map.put(key, serializer.serialize(map.get(key)));
                } catch (Exception e) {
                    throw new InvalidRequestException("Error doing serialization");                }
            }
        }

        sb.append(" WHERE id=:id");

        try {
            Update update = handle.createStatement(sb.toString());
            map.put("updated",System.currentTimeMillis());
            map.put("id",id);
            update = update.bindFromMap(map);
            update.execute();
        } catch(DBIException e) {
            throw new JubiqPersistenceException(e);
        } finally {
            handle.close();
        }
    }

    protected void generateGetSql() {
        getSql = "SELECT * from " + tableName + " WHERE id = :id AND deleted IS NULL";
    }

    protected void generateGetAllSql() {
        getAllSql = "SELECT * FROM " + tableName + " WHERE deleted IS NULL ORDER BY created DESC";
    }

    protected void generateRemoveSql() {
        removeSql = "UPDATE " + tableName + " SET deleted = :deleted WHERE id=:id";
    }

    protected void generateCountSql() {
        countSql = "SELECT COUNT(*) FROM " + tableName + " WHERE deleted IS NULL";
    }

    protected void generateInsertSql() {
        StringBuilder insertQuery = new StringBuilder();
        insertQuery.append("INSERT INTO ").append(tableName).append(" (");

        for (String columnName : entityFields.values()) {
            insertQuery.append(columnName).append(",");
        }
        insertQuery.delete(insertQuery.lastIndexOf(","), insertQuery.length());

        insertQuery.append(") VALUES(");
        for (String columnName : entityFields.values()) {
            insertQuery.append(":").append(columnName).append(",");
        }

        insertQuery.delete(insertQuery.lastIndexOf(","), insertQuery.length());
        insertQuery.append(")");
        insertSql = insertQuery.toString();
    }

    protected void generateUpdateSql() {
        StringBuilder updateQuery = new StringBuilder();
        updateQuery.append("UPDATE ").append(tableName).append(" SET ");

        for (String columnName : entityFields.values()) {
            updateQuery.append(columnName).append("=:").append(columnName).append(",");
        }
        updateQuery.delete(updateQuery.lastIndexOf(","), updateQuery.length());
        updateQuery.append(" WHERE id=:id");

        updateSql = updateQuery.toString();
    }

    protected void generateSqlQueries() {
        generateCountSql();
        generateGetAllSql();
        generateGetSql();
        generateInsertSql();
        generateRemoveSql();
        generateUpdateSql();

        /*
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("SQL Strings constructed for " + entityClass);
            LOGGER.debug("getSql=" + getSql);
            LOGGER.debug("removeSql=" + removeSql);
            LOGGER.debug("countSql=" + countSql);
            LOGGER.debug("getAllSql=" + getAllSql);
            LOGGER.debug("insertSql=" + insertSql);
            LOGGER.debug("updateSql=" + updateSql);
        }
        */
    }

    public ID randomId() {
        if(Long.class.equals(idType)) {
            return (ID)randomLong();
        }
        if(String.class.equals(idType)) {
            return (ID)randomString();
        }
        return null;
    }

    private Long randomLong() {
        Random random = new Random();
        long range = 1999999999L - 1000000000L;
        long fraction = (long)(range * random.nextDouble());
        return 1000000000L + fraction;
    }

    private String randomString() {
        return UUID.randomUUID().toString();
    }
}
