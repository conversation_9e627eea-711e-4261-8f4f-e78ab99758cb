--liquibase formatted sql
--changeset thanhlx:1015
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:CREATE','Allow to create label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:FIND','Allow to search for label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:READ','Allow to view label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:DELETE','Allow to delete label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:UPDATE','Allow to update label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:SUBMIT','Allow to submit label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:VALIDATE','Allow to validate label',1447653450436,NULL,NULL);
INSERT INTO permissions (id,description,created,updated,deleted) VALUES ('API:LABEL:REJECT','Allow to reject label',1447653450436,NULL,NULL);
--rollback;