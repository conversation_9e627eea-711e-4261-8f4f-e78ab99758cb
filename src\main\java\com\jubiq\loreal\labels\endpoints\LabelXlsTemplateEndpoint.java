package com.jubiq.loreal.labels.endpoints;

import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.labels.models.LabelXlsTemplate;
import com.jubiq.loreal.labels.services.LabelXlsTemplateService;

import javax.ws.rs.Consumes;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

import javax.inject.Inject;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;

@Path("/api/label-xls-templates")
@Api("Export Label Templates")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON,APPLICATION_FORM_URLENCODED})
public class LabelXlsTemplateEndpoint extends JubiqEndpoint<Long, LabelXlsTemplate> {
    @Inject
    public LabelXlsTemplateEndpoint(LabelXlsTemplateService labelXlsTemplateService) {
        this.service = labelXlsTemplateService;
    }
}
