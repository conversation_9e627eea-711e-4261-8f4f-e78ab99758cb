package com.jubiq.loreal.labels.models;

/**
 * Created by vietnq2 on 1/19/16.
 */
public enum LabelExportedField {
    id("Id"),
    notificationId("Requests"),
    notificationIds("Requests"),
    preservation("Bao quan"),
    barCode("Bar Code"),
    alarm("<PERSON>h bao"),
    importer("<PERSON>u trach nhiem va nhap khau boi"),
    owner("Chủ sở hữu"),
    uses("Cong dung, hieu qua"),
    followerEmail("Email cua nhan su MKT dang theo doi nhan phu"),
    creatorEmail("Email cua nhan su MKT tao nhan phu"),
    assigneeEmail("Email cua nhan su SCI xu ly nhan phu"),
    userManual("Huong dan su dung"),
    expirationDate("Ngay het han"),
    manufacturingDate("Ngay san xuat"),
    followerFullName("Nhan su MKT dang theo doi nhan phu"),
    creatorFullName("Nhan su MKT tao nhan phu"),
    assigneeFullName("Nhan su SCI xu ly nhan phu"),
    distributor("Phan phoi boi"),
    sapCode("SAP Code"),
    manufacturer("San xuat tai"),
    davNotificationNumber("So cong bo"),
    hotline("So dien thoai tu van khach hang"),
    lotNumber("So lo"),
    productName("Ten san pham"),
    ingredients("Thanh phan"),
    colorIngredients("Thanh phan cua mau trang diem"),
    baseIngredients("Thanh phan dong san pham"),
    netVolume("The tich thuc/ khoi luong tinh"),
    other("Thong tin khac"),
    status("Status"),
    submittedDate("Ngay MKT submit"),
    sciValidatedDate("Ngay SCI validate"),
    logisticValidatedDate("Ngay Logistic validate"),
    brandName("Nhan hang");

    public String displayName;

    LabelExportedField(String displayName) {
        this.displayName = displayName;
    }
}
