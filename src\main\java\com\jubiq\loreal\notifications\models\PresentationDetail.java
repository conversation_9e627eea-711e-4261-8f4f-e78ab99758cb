package com.jubiq.loreal.notifications.models;

import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.notifications.util.Utils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Created by vietnq2 on 1/18/16.
 */
public class PresentationDetail extends JubiqEntity<Long> {
    public Long notificationId;
    public String productCode;
    public String barCode;
    public String formulaNumber;
    public String filCode;
    public List<Ingredient> ingredients;
    public String compositeNumber;
    public String netWeight;
    public String volume;
    public String shadeName;
    public String shadeCode;
    public String individualIntendedUse;
    public Long manufacturerId;
    public String manufacturerName;
    public String manufacturerAddress;
    public String manufacturerPhoneNumber;
    public String manufacturerFaxNumber;
    public Long manufacturerCountryId;
    public String manufacturerCountryName;
    public Long assemblerId;
    public String assemblerName;
    public String assemblerAddress;
    public String assemblerPhoneNumber;
    public String assemblerFaxNumber;
    public Long assemblerCountryId;
    public String assemblerCountryName;
    public AssemblerType assemblerType;
    public String individualWarning;
    public String commonShadeName;
    public Integer discontinued;
    public Date officialDiscontinuedDate;
    public Date intentDiscontinuedDate;
    public Long exporterId;
    public String exporterName;
    public String exporterAddress;
    public String exporterPhoneNumber;
    public String exporterFaxNumber;
    public Long exporterCountryId;
    public String exporterCountryName;
    @NotAColumn
    public Set<String> differProperties= new HashSet<>();
    @NotAColumn
    public Map<Long, String> duplicateNotifications;

    /**
     * Map chua key la cac gia tri bi duplicate va value la id cua noti bi duplicate ( lay theo fil,shade, fal)
     */
    @NotAColumn
    public Map<String, Set<Long>> mapDuplicateNotification;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PresentationDetail that = (PresentationDetail) o;
        this.compareFields(that);
        return Objects.equals(productCode, that.productCode) && Objects.equals(barCode, that.barCode) && Objects.equals(formulaNumber, that.formulaNumber) && Objects.equals(filCode, that.filCode) && Objects.equals(compositeNumber, that.compositeNumber) && Objects.equals(netWeight, that.netWeight) && Objects.equals(volume, that.volume) && Objects.equals(shadeName, that.shadeName) && Objects.equals(shadeCode, that.shadeCode) && Objects.equals(individualIntendedUse, that.individualIntendedUse) && Objects.equals(manufacturerName, that.manufacturerName) && Objects.equals(manufacturerCountryName, that.manufacturerCountryName) && Objects.equals(assemblerName, that.assemblerName) && Objects.equals(assemblerAddress, that.assemblerAddress) && Objects.equals(assemblerPhoneNumber, that.assemblerPhoneNumber) && Objects.equals(assemblerFaxNumber, that.assemblerFaxNumber) && Objects.equals(assemblerCountryName, that.assemblerCountryName) && assemblerType == that.assemblerType && Objects.equals(individualWarning, that.individualWarning) && Objects.equals(commonShadeName, that.commonShadeName) && Objects.equals(discontinued, that.discontinued) && Objects.equals(officialDiscontinuedDate, that.officialDiscontinuedDate) && Objects.equals(intentDiscontinuedDate, that.intentDiscontinuedDate) && Objects.equals(exporterName, that.exporterName) && Objects.equals(exporterAddress, that.exporterAddress) && Objects.equals(exporterPhoneNumber, that.exporterPhoneNumber) && Objects.equals(exporterFaxNumber, that.exporterFaxNumber) && Objects.equals(exporterCountryId, that.exporterCountryId) && Objects.equals(exporterCountryName, that.exporterCountryName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(productCode, barCode, formulaNumber, filCode, compositeNumber, netWeight, volume, shadeName, shadeCode, individualIntendedUse, manufacturerName, manufacturerCountryName, assemblerName, assemblerAddress, assemblerPhoneNumber, assemblerFaxNumber, assemblerCountryName, assemblerType, individualWarning, commonShadeName, discontinued, officialDiscontinuedDate, intentDiscontinuedDate, exporterName, exporterAddress, exporterPhoneNumber, exporterFaxNumber, exporterCountryId, exporterCountryName);
    }

    public void compareFields(PresentationDetail other) {
        Class<?> clazz = getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object value1 = field.get(this);
                Object value2 = field.get(other);
                if(value1 == null && value2 == null) continue;
                if(
//                        field.getName().equals("netWeight")
//                        || field.getName().equals("volume")
//                        || field.getName().equals("productCode")
                            field.getName().equals("intentDiscontinuedDate")
                        || field.getName().equals("officialDiscontinuedDate")
                        || field.getName().equals("duplicateNotifications")
                        || field.getName().equals("mapDuplicateNotification")
//                        || field.getName().equals("barCode")
                        || field.getName().equals("ingredients")
                        || field.getName().equals("manufacturerId")
                        || field.getName().equals("manufacturerPhoneNumber")
                        || field.getName().equals("manufacturerFaxNumber")
                        || field.getName().equals("manufacturerAddress")
                        || field.getName().equals("assemblerId")
                        || field.getName().equals("assemblerPhoneNumber")
                        || field.getName().equals("assemblerFaxNumber")
                        || field.getName().equals("assemblerCountryId")
                        || field.getName().equals("assemblerAddress")
                        || field.getName().equals("assemblerName")
                        || field.getName().equals("manufacturerCountryId")
                        || field.getName().equals("differProperties")
                        || field.getName().equals("exporterCountryId")
                        || field.getName().equals("exporterId")
                        || field.getName().equals("exporterAddress")
                        || field.getName().equals("exporterPhoneNumber")
                        || field.getName().equals("exporterFaxNumber")
                )
                    continue;
                if(value1 instanceof Map && value2 instanceof Map && (((Map)value1).equals((Map)value2))){
                    continue;
                }
                if(value1 instanceof Date && value2 instanceof Date && Utils.convertDate((Date)value1).equals(Utils.convertDate((Date)value2))){
                    continue;
                }
                if (value1 != null && value2 == null || value1 == null && value2 != null || !value1.equals(value2)) {
                    this.differProperties.add(Utils.lowerCaseToCamelCase(getFieldName(field.getName())));
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }
    public String getFieldName(String field){
        String fieldReturn = field;
        switch (field){
            case "individualIntendedUse":
                fieldReturn = "intendedUse";
                break;
            case "individualWarning":
                fieldReturn = "warning";
                break;
            case "manufacturerCountryName":
                fieldReturn = "Country of Manufacturer";
                break;
                case "manufacturerName":
                fieldReturn = "Manufacturer";
                break;
                case "assemblerType":
                fieldReturn = "Type of assembler";
                break;
                case "exporterCountryName":
                fieldReturn = "Country of Exporter";
                break;
                case "exporterName":
                fieldReturn = "Exporter";
                break;
        }
        return fieldReturn;
    }

    //    @NotAColumn
//    public List<Label> labels;
}
