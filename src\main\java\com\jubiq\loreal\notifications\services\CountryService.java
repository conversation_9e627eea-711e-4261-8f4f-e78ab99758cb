package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.models.Country;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq on 1/5/16.
 */
@Singleton
public class CountryService extends JubiqService<Long,Country> {
    @Inject
    public CountryService(DaoFactory daoFactory) {
        this.dao = daoFactory.createDao(Country.class,Long.class);
    }

    public Country findByName(String name) {
        String sql = "select * from countries where name=:name and deleted is null";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("name",name);
        List<Country> countries = this.dao.search(sql,map);
        if(countries != null && countries.size() > 0) {
            return countries.get(0);
        } else {
            throw new EntityNotFoundException("No country found with name: " + name);
        }
    }

    @Override
    public void afterUpdate(Long id,Country entity) throws JubiqPersistenceException {
        //update country names in notification details
        updateManufacturerCountryName(entity);
        updateAssemblerCountryName(entity);
        updateExporterCountryName(entity);

        //update factory
        updateFactory(entity);

        //update exporter factory
        updateExporterFactory(entity);
    }
    private void updateManufacturerCountryName(Country entity) {
        String sql = "update presentation_details set manufacturer_country_name=:countryName,updated=:updated ";
        sql+="where manufacturer_country_id=:countryId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("countryName",entity.name);
        map.put("countryId",entity.id);
        this.dao.executeUpdate(sql,map);
    }

    private void updateAssemblerCountryName(Country entity) {
        String sql = "update presentation_details set assembler_country_name=:countryName,updated=:updated ";
        sql+="where assembler_country_id=:countryId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("countryName",entity.name);
        map.put("countryId",entity.id);
        this.dao.executeUpdate(sql,map);
    }
    private void updateExporterCountryName(Country entity) {
        String sql = "update presentation_details set exporter_country_name=:countryName,updated=:updated ";
        sql+="where exporter_country_id=:countryId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("countryName",entity.name);
        map.put("countryId",entity.id);
        this.dao.executeUpdate(sql,map);
    }
    private void updateFactory(Country entity) {
        String sql = "update factories set country_name=:countryName,updated=:updated where country_id=:countryId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("countryId",entity.id);
        map.put("countryName",entity.name);
        this.dao.executeUpdate(sql,map);
    }
      private void updateExporterFactory(Country entity) {
        String sql = "update exporter_factories set country_name=:countryName,updated=:updated where country_id=:countryId and deleted is null";
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("updated",System.currentTimeMillis());
        map.put("countryId",entity.id);
        map.put("countryName",entity.name);
        this.dao.executeUpdate(sql,map);
    }
}

