package com.jubiq.loreal.labels.dao;

import com.jubiq.loreal.common.persistence.JubiqDao;
import com.jubiq.loreal.labels.models.Label;
import org.skife.jdbi.v2.DBI;

/**
 * Created by THANHLX on 6/28/2018.
 */
public class LabelDao extends JubiqDao<Long,Label> {
    public LabelDao(DBI dbi) {
        super(dbi,Label.class,Long.class);
    }

    @Override
    protected void generateGetSql() {
        getSql = "SELECT * from " + tableName + " WHERE id = :id";
    }
}
