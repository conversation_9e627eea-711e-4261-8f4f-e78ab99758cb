package com.jubiq.loreal.common.exceptions;

import javax.ws.rs.core.Response;

/**
 * Created by vietnq2 on 11/18/15.
 */
public class AlreadyExistException extends JubiqException {
    public AlreadyExistException(Throwable e) {
        super(e);
    }

    public AlreadyExistException(String message) {
        super(message);
    }

    public AlreadyExistException(String message, Throwable e) {
        super(message, e);
    }

    @Override
    public Response.Status getStatus() {
        return JubiqErrorType.ALREADY_EXIST.httpStatus;
    }

    @Override
    public String getErrorCode() {
        return JubiqErrorType.ALREADY_EXIST.name();
    }
}
