package com.jubiq.loreal.cra.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.GroupService;
import com.jubiq.loreal.umgr.services.SessionService;
import com.jubiq.loreal.umgr.services.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Created by AI Assistant on 7/9/25.
 * Service for Quick Login functionality for GM/CM approval
 */
@Singleton
public class QuickLoginService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(QuickLoginService.class);
    
    // Group IDs for GM and CM - these should be configured based on your system
    private static final Long GM_GROUP_ID = 2L; // General Manager group
    private static final Long CM_GROUP_ID = 3L; // Category Manager group
    
    @Inject
    private CraRequestService craRequestService;
    
    @Inject
    private UserService userService;
    
    @Inject
    private GroupService groupService;
    
    @Inject
    private SessionService sessionService;
    
    /**
     * Authenticate user using quick login hash and create session
     * @param craRequestId CRA Request ID
     * @param loginHash Quick login hash (GM or CM)
     * @param email Email of the user to authenticate
     * @return JubiqSession for the authenticated user
     */
    public JubiqSession authenticateQuickLogin(Long craRequestId, String loginHash, String email)
            throws JubiqPersistenceException, EntityNotFoundException, InvalidRequestException {
        
        // Get CRA request
        CraRequest craRequest = craRequestService.get(craRequestId);
        if (craRequest == null) {
            throw new EntityNotFoundException("CRA Request not found with id: " + craRequestId);
        }
        
        // Validate login hash
        boolean isValidHash = false;
        String userType = null;

        if (loginHash.equals(craRequest.gmLoginHash)) {
            isValidHash = true;
            userType = "GM";
        } else if (loginHash.equals(craRequest.cmLoginHash)) {
            isValidHash = true;
            userType = "CM";
        }

        if (!isValidHash) {
            throw new InvalidRequestException("Invalid quick login hash");
        }

        // Find user by email
        User targetUser = findUserByEmail(email);
        if (targetUser == null) {
            throw new EntityNotFoundException("No user found with email: " + email);
        }
        
        // Create session for the user
        JubiqSession session = createQuickLoginSession(targetUser, craRequestId, userType);
        
        LOGGER.info("Quick login successful for {} user {} on CRA request {}",
                   userType, targetUser.email, craRequestId);
        
        return session;
    }
    
    /**
     * Find user by email address
     */
    private User findUserByEmail(String email) throws JubiqPersistenceException {
        try {
            return userService.findByEmail(email);
        } catch (EntityNotFoundException e) {
            return null;
        }
    }
    
    /**
     * Create a session for quick login user
     */
    private JubiqSession createQuickLoginSession(User user, Long craRequestId, String userType) 
            throws JubiqPersistenceException, EntityNotFoundException {
        
        JubiqSession session = new JubiqSession();
        session.userEmail = user.email;
        session.userId = user.id;
        session.expiresIn = Integer.valueOf(3600 * 2); // 2 hours for quick login
        session.grantType = "quick_login";
        session.fullName = user.fullName + " (Quick Login " + userType + ")";
        
        Group group = groupService.get(user.groupId);
        session.groupName = group.name;
        session.groupId = group.id;
        session.permissions = groupService.groupPermissions(user.groupId);
        
        // Add special permission for CRA approval
        session.permissions.add("API:CRA:QUICK_LOGIN");
        session.permissions.add("API:CRA:APPROVE");
        
        // Create session in database
        session = sessionService.create(session, null);
        
        return session;
    }
    
    /**
     * Validate if a session is from quick login and has access to specific CRA request
     */
    public boolean isQuickLoginSession(JubiqSession session, Long craRequestId) {
        return "quick_login".equals(session.grantType) && 
               session.permissions.contains("API:CRA:QUICK_LOGIN");
    }
    
    /**
     * Invalidate quick login hashes after approval/rejection
     */
    public void invalidateQuickLoginHashes(Long craRequestId) throws JubiqPersistenceException {
        CraRequest craRequest = craRequestService.get(craRequestId);
        if (craRequest != null) {
            craRequest.gmLoginHash = null;
            craRequest.cmLoginHash = null;
            craRequestService.getDao().update(craRequest);
            
            LOGGER.info("Quick login hashes invalidated for CRA request {}", craRequestId);
        }
    }
}
