package com.jubiq.loreal.notifications.models.enumerations;

/**
 * Created by vietnq2 on 11/25/15.
 */
public enum NotificationStatus {
    WAIT_FOR_MKT_SUBMISSION,
    MEDICAL_DEVICE_CLASSIFICATION,
    WAIT_FOR_SC_VALIDATION,
    WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN,
    WAIT_FOR_SCI_VALIDATION,
    WAIT_FOR_CFS_INCI,
    WAIT_FOR_CFS,
    WAIT_FOR_INCI,
    WAIT_FOR_SUBMISSION,
    WAIT_FOR_NOTIFICATION_NUMBER,
    REJECTED,
    SC_REJECTED,
    COMPLETED,
    CANC<PERSON>LED,
    WAIT_FOR_CANCEL,
    REC<PERSON>LE<PERSON>,
    WAIT_FOR_REJECTION
}
