--liquibase formatted sql
--changeset thanhlx:2003

INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('expiredCfs.alert.email.template', 'Notification ${notificationId} (Product Name: ${productName}) has not received CFS. <br> Please click <a href="http://***********/notification/process/${notificationId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('expiredInci.alert.email.template', 'Notification ${notificationId} (Product Name: ${productName}) has not received INCI. <br> Please click <a href="http://***********/notification/process/${notificationId}">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

--rollback;