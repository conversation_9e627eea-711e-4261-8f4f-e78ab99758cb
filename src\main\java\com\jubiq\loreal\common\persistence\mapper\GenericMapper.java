package com.jubiq.loreal.common.persistence.mapper;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jubiq.loreal.common.model.JubiqEntity;
import com.jubiq.loreal.common.util.NotAColumn;
import com.jubiq.loreal.common.util.Serialized;
import com.jubiq.loreal.common.util.Serializer;
import org.skife.jdbi.v2.StatementContext;
import org.skife.jdbi.v2.tweak.ResultSetMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by vietnq on 11/16/15.
 */
public class GenericMapper<T extends JubiqEntity> implements ResultSetMapper<T> {
    private Class<T> entityClass;
    private Class<?> idType;
    private Map<Field, String> entityFields = new LinkedHashMap<Field, String>();
    private static Logger LOGGER = LoggerFactory.getLogger(GenericMapper.class);
    private ObjectMapper jsonMapper;

    public GenericMapper(Class<T> entityClass, Map<Field, String> entityFields, Class<?> idType) {
        this.entityClass = entityClass;
        this.entityFields = entityFields;
        this.jsonMapper = new ObjectMapper();
        this.idType = idType;
    }
    @Override
    public T map(int i, ResultSet rs, StatementContext statementContext) throws SQLException {

        try {
            T entity = entityClass.newInstance();
            for(Map.Entry<Field,String> entry : entityFields.entrySet()) {
                Class<?> type = entry.getKey().getType();

                if(entry.getKey().isAnnotationPresent(NotAColumn.class)) {
                    continue;
                }

                Object value = null;

                if (entry.getKey().isAnnotationPresent(Serialized.class)) {

                    String jsonString = rs.getString(entry.getValue());

                    if (!rs.wasNull()) {
                        Serialized serialized = entry.getKey().getAnnotation(Serialized.class);
                        Serializer serializer = (Serializer) serialized.serializer().newInstance();
                        value = serializer.deSerialize(entry.getKey().getType(), jsonString);
                    }
                } else if (type.equals(Long.class)) {
                    long val = rs.getLong(entry.getValue());
                    if (!rs.wasNull()) {
                        value = val;
                    }
                } else if (type.equals(Integer.class)) {
                    int val = rs.getInt(entry.getValue());
                    if (!rs.wasNull()) {
                        value = val;
                    }
                } else if (type.isEnum()) {
                    Object val = rs.getObject(entry.getValue());
                    if (!rs.wasNull()) {
                        Method m = type.getMethod("valueOf", String.class);
                        value = m.invoke(entry.getKey().getType(), val);
                    } else {
                        val = false;
                    }

                } else if(type.equals(Boolean.class)) {
                    Boolean val = rs.getBoolean(entry.getValue());
                    if(!rs.wasNull()) {
                        value = val;
                    }
                } else if (Collection.class.isAssignableFrom(type)) {
                    String jsonString = rs.getString(entry.getValue());
                    if (!("".equals(jsonString) || rs.wasNull())) {
                        //convert to collection of java object from a json string
                        ParameterizedType pt = (ParameterizedType) entry.getKey().getGenericType();
                        Class<?> clazz = (Class<?>) pt.getActualTypeArguments()[0];
                        JavaType collectionType = jsonMapper.getTypeFactory().
                                constructCollectionType(Collection.class, clazz);
                        value = jsonMapper.readValue(jsonString, collectionType);
                    }

                } else {
                    Object val = rs.getObject(entry.getValue());
                    if (!rs.wasNull()) {
                        value = val;
                    }
                }
                if("id".equals(entry.getValue())) {
                    if(Long.class.equals(idType)) {
                        value = rs.getLong("id");
                    }
                    if(String.class.equals(idType)) {
                        value = rs.getString("id");
                    }
                }
                // Set the value on the entity object
                entry.getKey().set(entity, value);
                //TODO handle more data type if needed
            }
            return entity;
        } catch (InstantiationException e) {
            LOGGER.error("exception when mapping class {}",entityClass.getSimpleName(),e);
        } catch (IllegalAccessException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        } catch (NoSuchMethodException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        } catch (InvocationTargetException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        } catch (JsonMappingException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        } catch (JsonParseException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        } catch (IOException e) {
            LOGGER.error("exception when mapping class {}", entityClass.getSimpleName(), e);
        }
        return null;
    }

    public Map<String,Object> getBindMap(T entity) {
        Map<String,Object> map = new HashMap<String, Object>();
        try {
            for(Map.Entry<Field,String> entry : entityFields.entrySet()) {
                Field field = entry.getKey();
                if(field.isAnnotationPresent(NotAColumn.class)) {
                    continue;
                }
                if (Collection.class.isAssignableFrom(field.getType()) || Map.class.isAssignableFrom(field.getType())) {
                    map.put(entry.getValue(),field.get(entity) == null ? null : jsonMapper.writeValueAsString(field.get(entity)));
                } else if (field.getType().isEnum()) {
                    map.put(entry.getValue(),field.get(entity) == null ? null : field.get(entity).toString());
                } else if (field.isAnnotationPresent(Serialized.class)) {

                    Serialized serialized = field.getAnnotation(Serialized.class);
                    Serializer serializer = (Serializer) serialized.serializer().newInstance();

                    map.put(entry.getValue(), serializer.serialize(field.get(entity)));
                } else {
                    map.put(entry.getValue(),field.get(entity));
                }
            }

        } catch (JsonProcessingException e) {
            LOGGER.error("exception when creating bind map for class {}", entityClass.getSimpleName(), e);
        } catch (IllegalAccessException e) {
            LOGGER.error("exception when creating bind map class {}", entityClass.getSimpleName(), e);
        } catch (InstantiationException e) {
            LOGGER.error("exception when creating bind map class {}", entityClass.getSimpleName(), e);
        } catch (Exception e) {
            LOGGER.error("exception when creating bind map class {}", entityClass.getSimpleName(), e);
        }

        return map;
    }
}
