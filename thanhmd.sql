update filters set filters.query = '(dav_expiring_date  <= ''end_date'') AND (dav_expiring_date  >= ''start_date'') AND not exists (select 1 from histories h where h.object_id = notifications.id and h.action = ''renotify'' )' where id like '%EXPIRED%';

update filters set filters.`query`= 'discontinued = 1 AND not exists (select 1 from histories h where h.object_id = notifications.id and h.action = ''renotify'' )'
where id like '%DISCONTINUED%';

INSERT INTO `loreal`.`configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('update-notification.alert.email.template', 'Notification ${notificationId} has updated fields: ${differFields}.\r\n<br>\r\nPlease click <a href=\"http://************/notification/process/${notificationId}\">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `loreal`.`configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('update-label.alert.email.template', 'Label ${labelId} has updated fields: ${differFields}.\r\n<br>\r\nPlease click <a href=\"http://************/label/process/${labelId}\">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `loreal`.`configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`) VALUES ('update-advertising.alert.email.template', 'Advertisings ${advertisingId} has updated fields: ${differFields}.\r\n<br>\r\nPlease click <a href=\"http://************/advertising/process/${advertisingId}\">here</a> to view detail', NULL, NULL, NULL, NULL, NULL);

ALTER TABLE notifications
    ADD noti_type varchar(255);
ALTER TABLE notifications
    ADD reason_urgent varchar(255);
ALTER TABLE notifications
    ADD content_reason_other varchar(255);

ALTER TABLE labels RENAME COLUMN notification_id TO notification_ids;