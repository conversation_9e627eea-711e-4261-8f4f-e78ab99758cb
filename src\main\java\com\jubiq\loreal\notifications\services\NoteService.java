package com.jubiq.loreal.notifications.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.LorealConstants;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.notifications.dao.NotificationDao;
import com.jubiq.loreal.notifications.models.Alert;
import com.jubiq.loreal.notifications.models.Note;
import com.jubiq.loreal.notifications.models.Notification;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vietnq2 on 1/27/16.
 */
@Singleton
public class NoteService extends JubiqService<Long,Note> {
    private NotificationDao notificationDao;
    private AlertService alertService;
    @Inject
    public NoteService(DaoFactory daoFactory,AlertService alertService) {
        this.dao = daoFactory.createDao(Note.class,Long.class);
        this.notificationDao = daoFactory.getNotificationDao();
        this.alertService = alertService;
    }

    public List<Note> findByNotification(Long notificationId) {
        String sql = "select * from notes where notification_id=:notificationId and deleted is null order by created desc";
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("notificationId",notificationId);
        return this.dao.search(sql,map);
    }

    @Override
    public Note create(Note entity, JubiqSession session) throws JubiqPersistenceException {
        Notification notification = notificationDao.get(entity.notificationId);
        entity.creatorFullName = session.fullName;
        entity.creatorId = session.userId;

        Note note = super.create(entity,session);

        Alert alert = new Alert(session.userId,session.fullName,"note");
        alert.params.put("notificationId",entity.notificationId.toString());
        alert.params.put("productName",notification.productName);
        alertService.createAlert(alert, LorealConstants.SCI_MANAGER_GROUP_ID,notification.assigneeId);
        alertService.createAlert(alert, null,notification.followerId);
        return note;
    }
}

