package com.jubiq.loreal.quartz;

import org.quartz.Job;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.spi.JobFactory;
import org.quartz.spi.TriggerFiredBundle;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;

/**
 * Created by lamlt on 13/09/19.
 */
@Singleton
public class SchedulerJobFactory implements JobFactory {

  @Inject
  private Injector injector;

  @Override
  public Job newJob(TriggerFiredBundle bundle, Scheduler scheduler) throws SchedulerException {
    return (Job) injector.getInstance(bundle.getJobDetail()
            .getJobClass());
  }

}